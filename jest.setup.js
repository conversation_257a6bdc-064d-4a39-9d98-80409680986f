import '@testing-library/jest-dom'

// Set up test environment variables
process.env.DATABASE_URL = 'file:./test.db'
process.env.NEXTAUTH_SECRET = 'test-secret'
process.env.NEXTAUTH_URL = 'http://localhost:3000'

// Polyfill Web APIs for Next.js server components
import { TextEncoder, TextDecoder } from 'util'

// Mock Web APIs that Next.js server components expect
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Mock Request and Response for Next.js server components
global.Request = class MockRequest {
  constructor(input, init = {}) {
    this.url = typeof input === 'string' ? input : input.url
    this.method = init.method || 'GET'
    this.headers = new Map(Object.entries(init.headers || {}))
    this.body = init.body
    this._bodyInit = init.body
  }

  async json() {
    if (typeof this._bodyInit === 'string') {
      return JSON.parse(this._bodyInit)
    }
    return this._bodyInit || {}
  }

  async text() {
    if (typeof this._bodyInit === 'string') {
      return this._bodyInit
    }
    return JSON.stringify(this._bodyInit || {})
  }

  async formData() {
    return new FormData()
  }

  async arrayBuffer() {
    return new ArrayBuffer(0)
  }

  clone() {
    return new MockRequest(this.url, {
      method: this.method,
      headers: Object.fromEntries(this.headers),
      body: this._bodyInit
    })
  }
}

global.Response = class MockResponse {
  constructor(body, init = {}) {
    this.body = body
    this.status = init.status || 200
    this.statusText = init.statusText || 'OK'
    this.headers = new Map(Object.entries(init.headers || {}))
    this.ok = this.status >= 200 && this.status < 300
  }

  async json() {
    if (typeof this.body === 'string') {
      return JSON.parse(this.body)
    }
    return this.body
  }

  async text() {
    if (typeof this.body === 'string') {
      return this.body
    }
    return JSON.stringify(this.body)
  }

  clone() {
    return new MockResponse(this.body, {
      status: this.status,
      statusText: this.statusText,
      headers: Object.fromEntries(this.headers)
    })
  }

  static json(data, init = {}) {
    return new MockResponse(JSON.stringify(data), {
      ...init,
      headers: {
        'Content-Type': 'application/json',
        ...init.headers
      }
    })
  }
}

// Mock Headers
global.Headers = class MockHeaders extends Map {
  constructor(init) {
    super()
    if (init) {
      if (Array.isArray(init)) {
        for (const [key, value] of init) {
          this.set(key, value)
        }
      } else if (typeof init === 'object') {
        for (const [key, value] of Object.entries(init)) {
          this.set(key, value)
        }
      }
    }
  }

  append(name, value) {
    const existing = this.get(name)
    if (existing) {
      this.set(name, `${existing}, ${value}`)
    } else {
      this.set(name, value)
    }
  }

  delete(name) {
    super.delete(name.toLowerCase())
  }

  get(name) {
    return super.get(name.toLowerCase())
  }

  has(name) {
    return super.has(name.toLowerCase())
  }

  set(name, value) {
    super.set(name.toLowerCase(), String(value))
  }
}

// Mock FormData
global.FormData = class MockFormData {
  constructor() {
    this._data = new Map()
  }

  append(name, value) {
    if (this._data.has(name)) {
      const existing = this._data.get(name)
      if (Array.isArray(existing)) {
        existing.push(value)
      } else {
        this._data.set(name, [existing, value])
      }
    } else {
      this._data.set(name, value)
    }
  }

  delete(name) {
    this._data.delete(name)
  }

  get(name) {
    const value = this._data.get(name)
    return Array.isArray(value) ? value[0] : value
  }

  getAll(name) {
    const value = this._data.get(name)
    return Array.isArray(value) ? value : value ? [value] : []
  }

  has(name) {
    return this._data.has(name)
  }

  set(name, value) {
    this._data.set(name, value)
  }

  entries() {
    return this._data.entries()
  }

  keys() {
    return this._data.keys()
  }

  values() {
    return this._data.values()
  }
}

// Mock URL and URLSearchParams
global.URL = class MockURL {
  constructor(url, base) {
    const fullUrl = base ? new URL(url, base).href : url
    const parsed = new URL(fullUrl)

    this.href = parsed.href
    this.origin = parsed.origin
    this.protocol = parsed.protocol
    this.host = parsed.host
    this.hostname = parsed.hostname
    this.port = parsed.port
    this.pathname = parsed.pathname
    this.search = parsed.search
    this.hash = parsed.hash
    this.searchParams = new URLSearchParams(parsed.search)
  }
}

// Mock crypto for Next.js
global.crypto = {
  randomUUID: () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  },
  getRandomValues: (array) => {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256)
    }
    return array
  }
}
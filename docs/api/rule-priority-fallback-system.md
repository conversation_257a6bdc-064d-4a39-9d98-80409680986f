# Rule Priority Fallback System API Documentation

This document provides comprehensive documentation for the Rule Priority Fallback System API endpoints, implementing the requirements from the Kiro protocol specification.

## Overview

The Rule Priority Fallback System provides a robust API for managing conditional link rules with priority handling, conflict resolution, error handling, and performance monitoring.

## Base URL

All API endpoints are prefixed with `/api/rules/`

## Authentication

All endpoints require authentication via session token. Include the session token in your requests.

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": ["Additional error details"],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## API Endpoints

### 1. Rule Management API Endpoints

#### 1.1 Condition CRUD Endpoints

##### GET /api/rules/conditions
Retrieve conditions with filtering and pagination.

**Query Parameters:**
- `linkId` (string, optional): Filter by link ID
- `type` (enum, optional): Filter by condition type
- `isActive` (boolean, optional): Filter by active status
- `priority` (number, optional): Filter by priority
- `limit` (number, optional): Number of results (1-100, default: 50)
- `offset` (number, optional): Offset for pagination (default: 0)
- `sortBy` (enum, optional): Sort field (priority, createdAt, updatedAt)
- `sortOrder` (enum, optional): Sort order (asc, desc)

**Response:**
```json
{
  "conditions": [...],
  "pagination": {
    "limit": 50,
    "offset": 0,
    "total": 100
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

##### POST /api/rules/conditions
Create a new condition with validation and conflict checking.

**Request Body:**
```json
{
  "linkId": "string",
  "type": "referrer|location|device|time|schedule",
  "priority": 100,
  "isActive": true,
  "rules": {...},
  "action": {
    "type": "show|hide|redirect",
    "value": "string",
    "alternateTitle": "string",
    "alternateIcon": "string",
    "metadata": {...}
  }
}
```

**Response:**
```json
{
  "condition": {...},
  "conflicts": [...],
  "warnings": [...],
  "performanceMetrics": {...},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

##### GET /api/rules/conditions/[conditionId]
Retrieve a specific condition by ID.

##### PUT /api/rules/conditions/[conditionId]
Update a specific condition.

##### DELETE /api/rules/conditions/[conditionId]
Delete a specific condition.

#### 1.2 Validation and Conflict Resolution Endpoints

##### POST /api/rules/conditions/validate
Validate a condition configuration without creating it.

**Request Body:**
```json
{
  "linkId": "string",
  "type": "referrer",
  "priority": 100,
  "isActive": true,
  "rules": {...},
  "action": {...},
  "validationLevel": "strict|moderate|lenient",
  "checkConflicts": true,
  "includeOptimizationSuggestions": true
}
```

**Response:**
```json
{
  "valid": true,
  "validation": {
    "errors": [...],
    "warnings": [...],
    "semanticIssues": [...],
    "performanceWarnings": [...],
    "securityConcerns": [...],
    "compatibilityIssues": [...],
    "suggestions": [...]
  },
  "conflicts": {...},
  "optimization": {...},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

##### GET /api/rules/conditions/conflicts
Detect conflicts in rule configurations.

**Query Parameters:**
- `linkId` (string, required): Link ID to analyze
- `includeWarnings` (boolean, optional): Include warnings (default: true)
- `includeSuggestions` (boolean, optional): Include suggestions (default: true)
- `analysisDepth` (enum, optional): Analysis depth (basic, detailed, comprehensive)

##### POST /api/rules/conditions/conflicts
Resolve conflicts in rule configurations.

**Request Body:**
```json
{
  "linkId": "string",
  "resolutionStrategy": "auto|priority_adjustment|rule_modification|manual",
  "conflictIds": ["string"],
  "dryRun": false,
  "preserveUserPreferences": true
}
```

#### 1.3 Performance Metrics Endpoints

##### GET /api/rules/conditions/performance
Get performance metrics for rule evaluation.

**Query Parameters:**
- `linkId` (string, optional): Filter by link ID
- `conditionId` (string, optional): Filter by condition ID
- `timeRange` (enum, optional): Time range (1h, 24h, 7d, 30d, 90d)
- `includeHistorical` (boolean, optional): Include historical data
- `includeBreakdown` (boolean, optional): Include performance breakdown
- `includeOptimizationSuggestions` (boolean, optional): Include optimization suggestions
- `aggregation` (enum, optional): Aggregation method (avg, p50, p95, p99, max)

### 2. Rule Testing and Simulation API Endpoints

#### 2.1 Rule Evaluation Simulation Endpoint

##### POST /api/rules/simulation/evaluate
Simulate rule evaluation for a given visitor context.

**Request Body:**
```json
{
  "linkId": "string",
  "visitorContext": {
    "referrer": "string",
    "userAgent": "string",
    "country": "string",
    "region": "string",
    "city": "string",
    "timezone": "string",
    "deviceType": "mobile|tablet|desktop",
    "platform": "string",
    "browser": "string",
    "now": "2024-01-01T00:00:00.000Z",
    "customHeaders": {...}
  },
  "simulationOptions": {
    "includePerformanceMetrics": true,
    "includeDebugInfo": false,
    "enableCaching": true,
    "simulateErrors": false,
    "errorRate": 0,
    "includeAlternativeOutcomes": false
  }
}
```

##### PUT /api/rules/simulation/evaluate
Simulate rule evaluation for multiple scenarios (batch processing).

#### 2.2 Rule Performance Testing Endpoint

##### POST /api/rules/simulation/performance
Run performance tests on rule evaluation.

**Request Body:**
```json
{
  "linkId": "string",
  "testConfiguration": {
    "duration": 60,
    "concurrency": 10,
    "requestsPerSecond": 50,
    "rampUpTime": 10,
    "testType": "load|stress|spike|endurance"
  },
  "visitorProfiles": [...],
  "options": {
    "enableCaching": true,
    "collectDetailedMetrics": true,
    "includePercentiles": true,
    "warmupRequests": 10
  }
}
```

##### GET /api/rules/simulation/performance
Get performance test history and results.

#### 2.3 Rule Conflict Analysis Endpoint

##### POST /api/rules/simulation/conflicts
Analyze rule conflicts and their impact.

**Request Body:**
```json
{
  "linkId": "string",
  "analysisType": "basic|comprehensive|predictive",
  "includeScenarios": true,
  "includeResolutionSuggestions": true,
  "simulateChanges": [...]
}
```

##### PUT /api/rules/simulation/conflicts
Perform what-if analysis for rule changes.

### 3. Monitoring and Analytics API Endpoints

#### 3.1 Performance Metrics API

##### GET /api/rules/monitoring/metrics
Get performance metrics for rule evaluation.

**Query Parameters:**
- `linkId` (string, optional): Filter by link ID
- `conditionId` (string, optional): Filter by condition ID
- `timeRange` (enum, optional): Time range (1h, 6h, 24h, 7d, 30d, 90d)
- `granularity` (enum, optional): Data granularity (minute, hour, day)
- `metrics` (array, optional): Metrics to include
- `aggregation` (enum, optional): Aggregation method
- `includeBreakdown` (boolean, optional): Include detailed breakdown
- `includeComparison` (boolean, optional): Include comparison data

##### POST /api/rules/monitoring/metrics/alerts
Configure performance alerts.

#### 3.2 Error Reporting Endpoints

##### POST /api/rules/monitoring/errors
Report a rule evaluation error.

**Request Body:**
```json
{
  "linkId": "string",
  "conditionId": "string",
  "errorType": "validation_error|evaluation_error|configuration_error|network_error|timeout_error|system_error|unknown_error",
  "errorMessage": "string",
  "errorCode": "string",
  "stackTrace": "string",
  "visitorContext": {...},
  "ruleConfiguration": {...},
  "severity": "low|medium|high|critical",
  "metadata": {...}
}
```

##### GET /api/rules/monitoring/errors
Retrieve error reports with filtering and analytics.

##### PUT /api/rules/monitoring/errors/resolve
Mark errors as resolved.

#### 3.3 Rule Usage Analytics

##### GET /api/rules/monitoring/analytics
Get rule usage analytics and insights.

**Query Parameters:**
- `linkId` (string, optional): Filter by link ID
- `conditionId` (string, optional): Filter by condition ID
- `timeRange` (enum, optional): Time range
- `granularity` (enum, optional): Data granularity
- `metrics` (array, optional): Metrics to include
- `segmentBy` (array, optional): Segmentation dimensions
- `includeComparison` (boolean, optional): Include comparison data
- `includeInsights` (boolean, optional): Include insights
- `includeRecommendations` (boolean, optional): Include recommendations

##### POST /api/rules/monitoring/analytics/report
Generate comprehensive usage reports.

**Request Body:**
```json
{
  "reportType": "summary|detailed|optimization|trends",
  "timeRange": "7d|30d|90d",
  "includeCharts": true,
  "format": "json|csv|pdf",
  "linkIds": ["string"],
  "conditionTypes": ["referrer", "location", "device", "time", "schedule"]
}
```

## Rate Limiting

API endpoints are rate-limited to prevent abuse:
- 1000 requests per hour for authenticated users
- 100 requests per hour for unauthenticated requests
- Higher limits available for premium users

## Caching

Responses are cached where appropriate:
- GET requests: 5-60 minutes depending on data volatility
- Performance metrics: 5 minutes
- Analytics data: 15 minutes
- Configuration data: 60 minutes

## Webhooks

Configure webhooks to receive real-time notifications:
- Performance alerts
- Error notifications
- Conflict detection
- Usage threshold alerts

## SDK Support

Official SDKs available for:
- JavaScript/TypeScript
- Python
- PHP
- Go

## Examples

See the `/examples` directory for complete implementation examples in various programming languages.

## Support

For API support and questions:
- Documentation: `/docs/api`
- Support: <EMAIL>
- Status: status.example.com

/**
 * Rule Engine Performance Monitor
 * 
 * Monitors and tracks performance metrics for rule evaluation
 */

import { performance } from 'perf_hooks'
import type { 
  PerformanceMetrics, 
  RuleEvaluationContext, 
  RuleEvaluationResult 
} from './types'
import { 
  PERFORMANCE_THRESHOLDS, 
  MONITORING_CONFIG,
  ERROR_HANDLING 
} from './constants'

/**
 * Performance alert information
 */
export interface PerformanceAlert {
  type: 'slow_evaluation' | 'high_error_rate' | 'cache_miss_spike' | 'memory_usage'
  threshold: number
  actualValue: number
  linkId: string
  conditionId?: string
  timestamp: Date
  severity: 'warning' | 'critical'
  metadata?: Record<string, any>
}

/**
 * Performance statistics aggregation
 */
export interface PerformanceStats {
  totalEvaluations: number
  averageEvaluationTime: number
  p50EvaluationTime: number
  p95EvaluationTime: number
  p99EvaluationTime: number
  errorRate: number
  cacheHitRate: number
  slowEvaluationCount: number
  timeoutCount: number
  lastUpdated: Date
}

/**
 * Performance data point for time series analysis
 */
export interface PerformanceDataPoint {
  timestamp: Date
  linkId: string
  conditionId?: string
  evaluationTime: number
  success: boolean
  cacheHit: boolean
  errorType?: string
  memoryUsage?: number
}

/**
 * Performance monitor class for tracking rule evaluation metrics
 */
export class RulePerformanceMonitor {
  private static instance: RulePerformanceMonitor
  private performanceData: PerformanceDataPoint[] = []
  private alertCallbacks: ((alert: PerformanceAlert) => void)[] = []
  private lastCleanup: Date = new Date()
  private evaluationTimes: Map<string, number[]> = new Map()
  private errorCounts: Map<string, number> = new Map()
  private cacheStats: { hits: number; misses: number } = { hits: 0, misses: 0 }

  private constructor() {
    // Start periodic cleanup
    this.startPeriodicCleanup()
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): RulePerformanceMonitor {
    if (!RulePerformanceMonitor.instance) {
      RulePerformanceMonitor.instance = new RulePerformanceMonitor()
    }
    return RulePerformanceMonitor.instance
  }

  /**
   * Start timing for a rule evaluation
   */
  public startTiming(evaluationId: string): number {
    return performance.now()
  }

  /**
   * End timing and record performance data
   */
  public endTiming(
    context: RuleEvaluationContext,
    result: RuleEvaluationResult,
    startTime: number
  ): PerformanceMetrics {
    const endTime = performance.now()
    const totalTime = endTime - startTime

    // Create performance metrics
    const metrics: PerformanceMetrics = {
      totalEvaluationTime: totalTime,
      conditionEvaluationTimes: result.performanceMetrics?.conditionEvaluationTimes || {},
      visitorContextParsingTime: result.performanceMetrics?.visitorContextParsingTime || 0,
      databaseQueryTime: result.performanceMetrics?.databaseQueryTime || 0,
      cacheHitRate: this.calculateCacheHitRate(),
      conditionsEvaluated: result.matchedConditions.length,
      memoryUsage: this.getMemoryUsage(),
    }

    // Record performance data point
    const dataPoint: PerformanceDataPoint = {
      timestamp: new Date(),
      linkId: context.link.id,
      evaluationTime: totalTime,
      success: !result.fallbackUsed && !result.errorDetails,
      cacheHit: result.metadata.strategy === 'cache',
      errorType: result.errorDetails ? this.categorizeError(result.errorDetails) : undefined,
      memoryUsage: metrics.memoryUsage,
    }

    this.recordDataPoint(dataPoint)
    this.checkPerformanceThresholds(context, metrics)

    return metrics
  }

  /**
   * Record cache hit
   */
  public recordCacheHit(linkId: string): void {
    this.cacheStats.hits++
  }

  /**
   * Record cache miss
   */
  public recordCacheMiss(linkId: string): void {
    this.cacheStats.misses++
  }

  /**
   * Record evaluation error
   */
  public recordError(linkId: string, errorType: string, conditionId?: string): void {
    const key = `${linkId}:${errorType}`
    const currentCount = this.errorCounts.get(key) || 0
    this.errorCounts.set(key, currentCount + 1)

    // Check error rate threshold
    this.checkErrorRateThreshold(linkId, errorType)
  }

  /**
   * Get performance statistics for a link
   */
  public getPerformanceStats(linkId?: string): PerformanceStats {
    const relevantData = linkId 
      ? this.performanceData.filter(d => d.linkId === linkId)
      : this.performanceData

    if (relevantData.length === 0) {
      return {
        totalEvaluations: 0,
        averageEvaluationTime: 0,
        p50EvaluationTime: 0,
        p95EvaluationTime: 0,
        p99EvaluationTime: 0,
        errorRate: 0,
        cacheHitRate: 0,
        slowEvaluationCount: 0,
        timeoutCount: 0,
        lastUpdated: new Date(),
      }
    }

    const evaluationTimes = relevantData.map(d => d.evaluationTime).sort((a, b) => a - b)
    const successfulEvaluations = relevantData.filter(d => d.success).length
    const cacheHits = relevantData.filter(d => d.cacheHit).length
    const slowEvaluations = relevantData.filter(d => d.evaluationTime > PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_WARNING).length
    const timeouts = relevantData.filter(d => d.evaluationTime > PERFORMANCE_THRESHOLDS.MAX_EVALUATION_TIME).length

    return {
      totalEvaluations: relevantData.length,
      averageEvaluationTime: evaluationTimes.reduce((sum, time) => sum + time, 0) / evaluationTimes.length,
      p50EvaluationTime: this.calculatePercentile(evaluationTimes, 0.5),
      p95EvaluationTime: this.calculatePercentile(evaluationTimes, 0.95),
      p99EvaluationTime: this.calculatePercentile(evaluationTimes, 0.99),
      errorRate: ((relevantData.length - successfulEvaluations) / relevantData.length) * 100,
      cacheHitRate: (cacheHits / relevantData.length) * 100,
      slowEvaluationCount: slowEvaluations,
      timeoutCount: timeouts,
      lastUpdated: new Date(),
    }
  }

  /**
   * Subscribe to performance alerts
   */
  public onAlert(callback: (alert: PerformanceAlert) => void): void {
    this.alertCallbacks.push(callback)
  }

  /**
   * Get recent performance data points
   */
  public getRecentData(linkId?: string, minutes: number = 60): PerformanceDataPoint[] {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000)
    return this.performanceData
      .filter(d => d.timestamp >= cutoff)
      .filter(d => !linkId || d.linkId === linkId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  /**
   * Clear performance data (for testing)
   */
  public clearData(): void {
    this.performanceData = []
    this.evaluationTimes.clear()
    this.errorCounts.clear()
    this.cacheStats = { hits: 0, misses: 0 }
  }

  /**
   * Get user-level performance metrics
   */
  public async getUserMetrics(userId: string, options: {
    timeRange: string
    granularity: string
    metrics: string[]
    aggregation: string
  }) {
    // Convert time range to actual time window
    const timeWindow = this.parseTimeRange(options.timeRange)
    const cutoff = new Date(Date.now() - timeWindow)

    // Filter data for the time range (in a real implementation, this would filter by userId)
    const relevantData = this.performanceData.filter(d => d.timestamp >= cutoff)

    const baseStats = this.getPerformanceStats()

    return {
      userId,
      timeRange: options.timeRange,
      granularity: options.granularity,
      aggregation: options.aggregation,
      metrics: {
        evaluation_time: {
          avg: baseStats.averageEvaluationTime,
          p50: baseStats.p50EvaluationTime,
          p95: baseStats.p95EvaluationTime,
          p99: baseStats.p99EvaluationTime
        },
        throughput: {
          total: baseStats.totalEvaluations,
          rate: baseStats.totalEvaluations / (timeWindow / (1000 * 60 * 60)) // per hour
        },
        error_rate: {
          percentage: baseStats.errorRate,
          count: Math.round(baseStats.totalEvaluations * baseStats.errorRate / 100)
        },
        cache_hit_rate: {
          percentage: baseStats.cacheHitRate,
          hits: Math.round(baseStats.totalEvaluations * baseStats.cacheHitRate / 100),
          misses: Math.round(baseStats.totalEvaluations * (100 - baseStats.cacheHitRate) / 100)
        }
      },
      dataPoints: this.generateTimeSeriesData(relevantData, options.granularity),
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Get condition-specific performance metrics
   */
  public async getConditionMetrics(conditionId: string, options: {
    timeRange: string
    granularity: string
    metrics: string[]
    aggregation: string
  }) {
    const timeWindow = this.parseTimeRange(options.timeRange)
    const cutoff = new Date(Date.now() - timeWindow)

    // Filter data for the specific condition (in a real implementation, this would filter by conditionId)
    const relevantData = this.performanceData.filter(d =>
      d.timestamp >= cutoff && d.conditionId === conditionId
    )

    const stats = this.calculateStatsFromData(relevantData)

    return {
      conditionId,
      timeRange: options.timeRange,
      granularity: options.granularity,
      aggregation: options.aggregation,
      metrics: {
        evaluation_time: stats.evaluationTime,
        throughput: stats.throughput,
        error_rate: stats.errorRate,
        cache_hit_rate: stats.cacheHitRate,
        condition_matches: stats.conditionMatches || { count: 0, rate: 0 }
      },
      dataPoints: this.generateTimeSeriesData(relevantData, options.granularity),
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Get link-specific performance metrics
   */
  public async getLinkMetrics(linkId: string, options: {
    timeRange: string
    granularity: string
    metrics: string[]
    aggregation: string
  }) {
    const timeWindow = this.parseTimeRange(options.timeRange)
    const cutoff = new Date(Date.now() - timeWindow)

    // Filter data for the specific link
    const relevantData = this.performanceData.filter(d =>
      d.timestamp >= cutoff && d.linkId === linkId
    )

    const stats = this.calculateStatsFromData(relevantData)

    return {
      linkId,
      timeRange: options.timeRange,
      granularity: options.granularity,
      aggregation: options.aggregation,
      metrics: {
        evaluation_time: stats.evaluationTime,
        throughput: stats.throughput,
        error_rate: stats.errorRate,
        cache_hit_rate: stats.cacheHitRate,
        fallback_usage: stats.fallbackUsage || { count: 0, rate: 0 }
      },
      dataPoints: this.generateTimeSeriesData(relevantData, options.granularity),
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Parse time range string to milliseconds
   */
  private parseTimeRange(timeRange: string): number {
    const timeRangeMap: Record<string, number> = {
      '1h': 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
      '90d': 90 * 24 * 60 * 60 * 1000
    }
    return timeRangeMap[timeRange] || timeRangeMap['24h']
  }

  /**
   * Calculate statistics from performance data points
   */
  private calculateStatsFromData(data: PerformanceDataPoint[]) {
    if (data.length === 0) {
      return {
        evaluationTime: { avg: 0, p50: 0, p95: 0, p99: 0 },
        throughput: { total: 0, rate: 0 },
        errorRate: { percentage: 0, count: 0 },
        cacheHitRate: { percentage: 0, hits: 0, misses: 0 },
        fallbackUsage: { count: 0, rate: 0 },
        conditionMatches: { count: 0, rate: 0 }
      }
    }

    const evaluationTimes = data.map(d => d.evaluationTime).sort((a, b) => a - b)
    const successfulEvaluations = data.filter(d => d.success).length
    const cacheHits = data.filter(d => d.cacheHit).length
    const errorCount = data.length - successfulEvaluations

    return {
      evaluationTime: {
        avg: evaluationTimes.reduce((sum, time) => sum + time, 0) / evaluationTimes.length,
        p50: this.calculatePercentile(evaluationTimes, 0.5),
        p95: this.calculatePercentile(evaluationTimes, 0.95),
        p99: this.calculatePercentile(evaluationTimes, 0.99)
      },
      throughput: {
        total: data.length,
        rate: data.length / (24) // per hour (simplified)
      },
      errorRate: {
        percentage: (errorCount / data.length) * 100,
        count: errorCount
      },
      cacheHitRate: {
        percentage: (cacheHits / data.length) * 100,
        hits: cacheHits,
        misses: data.length - cacheHits
      },
      fallbackUsage: {
        count: Math.floor(data.length * 0.1), // Mock 10% fallback usage
        rate: 10
      },
      conditionMatches: {
        count: Math.floor(data.length * 0.8), // Mock 80% condition match rate
        rate: 80
      }
    }
  }

  /**
   * Generate time series data for charts
   */
  private generateTimeSeriesData(data: PerformanceDataPoint[], granularity: string) {
    if (data.length === 0) {
      return []
    }

    // Group data by time intervals based on granularity
    const intervals = this.getTimeIntervals(granularity)
    const groupedData = new Map<string, PerformanceDataPoint[]>()

    // Initialize all intervals with empty arrays
    intervals.forEach(interval => {
      groupedData.set(interval, [])
    })

    // Group data points by interval
    data.forEach(point => {
      const interval = this.getIntervalKey(point.timestamp, granularity)
      const existing = groupedData.get(interval) || []
      existing.push(point)
      groupedData.set(interval, existing)
    })

    // Convert to time series format
    return Array.from(groupedData.entries()).map(([interval, points]) => ({
      timestamp: interval,
      value: points.length > 0 ? points.reduce((sum, p) => sum + p.evaluationTime, 0) / points.length : 0,
      count: points.length
    }))
  }

  /**
   * Get time intervals for the given granularity
   */
  private getTimeIntervals(granularity: string): string[] {
    const now = new Date()
    const intervals: string[] = []

    switch (granularity) {
      case 'minute':
        // Last 60 minutes
        for (let i = 59; i >= 0; i--) {
          const time = new Date(now.getTime() - i * 60 * 1000)
          intervals.push(time.toISOString().slice(0, 16)) // YYYY-MM-DDTHH:MM
        }
        break
      case 'hour':
        // Last 24 hours
        for (let i = 23; i >= 0; i--) {
          const time = new Date(now.getTime() - i * 60 * 60 * 1000)
          intervals.push(time.toISOString().slice(0, 13)) // YYYY-MM-DDTHH
        }
        break
      case 'day':
      default:
        // Last 30 days
        for (let i = 29; i >= 0; i--) {
          const time = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
          intervals.push(time.toISOString().slice(0, 10)) // YYYY-MM-DD
        }
        break
    }

    return intervals
  }

  /**
   * Get interval key for a timestamp based on granularity
   */
  private getIntervalKey(timestamp: Date, granularity: string): string {
    switch (granularity) {
      case 'minute':
        return timestamp.toISOString().slice(0, 16) // YYYY-MM-DDTHH:MM
      case 'hour':
        return timestamp.toISOString().slice(0, 13) // YYYY-MM-DDTHH
      case 'day':
      default:
        return timestamp.toISOString().slice(0, 10) // YYYY-MM-DD
    }
  }

  /**
   * Get active alerts for monitoring
   */
  public async getActiveAlerts(options: {
    linkId?: string
    conditionId?: string
    userId?: string
  }) {
    // Mock active alerts data - in a real implementation, this would query a database
    const mockAlerts = [
      {
        id: 'alert-1',
        metricType: 'evaluation_time',
        threshold: 100,
        currentValue: 125,
        severity: 'warning',
        message: 'Rule evaluation time exceeding threshold',
        timestamp: new Date(),
        linkId: options.linkId,
        conditionId: options.conditionId,
        userId: options.userId
      },
      {
        id: 'alert-2',
        metricType: 'error_rate',
        threshold: 5,
        currentValue: 8.2,
        severity: 'critical',
        message: 'High error rate detected',
        timestamp: new Date(),
        linkId: options.linkId,
        conditionId: options.conditionId,
        userId: options.userId
      }
    ]

    // Filter alerts based on options
    return mockAlerts.filter(alert => {
      if (options.linkId && alert.linkId !== options.linkId) return false
      if (options.conditionId && alert.conditionId !== options.conditionId) return false
      if (options.userId && alert.userId !== options.userId) return false
      return true
    })
  }

  /**
   * Record a performance data point
   */
  private recordDataPoint(dataPoint: PerformanceDataPoint): void {
    this.performanceData.push(dataPoint)

    // Update evaluation times map
    const times = this.evaluationTimes.get(dataPoint.linkId) || []
    times.push(dataPoint.evaluationTime)
    this.evaluationTimes.set(dataPoint.linkId, times)

    // Limit data size
    if (this.performanceData.length > MONITORING_CONFIG.METRICS_BATCH_SIZE * 100) {
      this.performanceData = this.performanceData.slice(-MONITORING_CONFIG.METRICS_BATCH_SIZE * 50)
    }
  }

  /**
   * Check performance thresholds and trigger alerts
   */
  private checkPerformanceThresholds(
    context: RuleEvaluationContext,
    metrics: PerformanceMetrics
  ): void {
    // Check evaluation time thresholds
    if (metrics.totalEvaluationTime > PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_CRITICAL) {
      this.triggerAlert({
        type: 'slow_evaluation',
        threshold: PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_CRITICAL,
        actualValue: metrics.totalEvaluationTime,
        linkId: context.link.id,
        timestamp: new Date(),
        severity: 'critical',
        metadata: { evaluationId: context.evaluationId },
      })
    } else if (metrics.totalEvaluationTime > PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_WARNING) {
      this.triggerAlert({
        type: 'slow_evaluation',
        threshold: PERFORMANCE_THRESHOLDS.SLOW_EVALUATION_WARNING,
        actualValue: metrics.totalEvaluationTime,
        linkId: context.link.id,
        timestamp: new Date(),
        severity: 'warning',
        metadata: { evaluationId: context.evaluationId },
      })
    }

    // Check memory usage
    if (metrics.memoryUsage && metrics.memoryUsage > PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE) {
      this.triggerAlert({
        type: 'memory_usage',
        threshold: PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE,
        actualValue: metrics.memoryUsage,
        linkId: context.link.id,
        timestamp: new Date(),
        severity: 'warning',
        metadata: { evaluationId: context.evaluationId },
      })
    }
  }

  /**
   * Check error rate threshold
   */
  private checkErrorRateThreshold(linkId: string, errorType: string): void {
    const recentData = this.getRecentData(linkId, 5) // Last 5 minutes
    const errorCount = recentData.filter(d => d.errorType === errorType).length
    const totalCount = recentData.length

    if (totalCount > 0) {
      const errorRate = (errorCount / totalCount) * 100
      if (errorRate > ERROR_HANDLING.ERROR_RATE_ALERT_THRESHOLD) {
        this.triggerAlert({
          type: 'high_error_rate',
          threshold: ERROR_HANDLING.ERROR_RATE_ALERT_THRESHOLD,
          actualValue: errorRate,
          linkId,
          timestamp: new Date(),
          severity: 'critical',
          metadata: { errorType, errorCount, totalCount },
        })
      }
    }
  }

  /**
   * Trigger a performance alert
   */
  private triggerAlert(alert: PerformanceAlert): void {
    console.warn(`[RulePerformanceMonitor] Alert: ${alert.type}`, alert)
    
    this.alertCallbacks.forEach(callback => {
      try {
        callback(alert)
      } catch (error) {
        console.error('[RulePerformanceMonitor] Error in alert callback:', error)
      }
    })
  }

  /**
   * Calculate cache hit rate
   */
  private calculateCacheHitRate(): number {
    const total = this.cacheStats.hits + this.cacheStats.misses
    return total > 0 ? (this.cacheStats.hits / total) * 100 : 0
  }

  /**
   * Get current memory usage
   */
  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed / 1024 / 1024 // Convert to MB
    }
    return 0
  }

  /**
   * Calculate percentile from sorted array
   */
  private calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0
    
    const index = Math.ceil(sortedArray.length * percentile) - 1
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))]
  }

  /**
   * Categorize error type
   */
  private categorizeError(errorMessage: string): string {
    if (errorMessage.includes('timeout')) return 'timeout'
    if (errorMessage.includes('validation')) return 'validation'
    if (errorMessage.includes('network')) return 'network'
    if (errorMessage.includes('cache')) return 'cache'
    return 'unknown'
  }

  /**
   * Start periodic cleanup of old data
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupOldData()
    }, MONITORING_CONFIG.METRICS_COLLECTION_INTERVAL)
  }

  /**
   * Clean up old performance data
   */
  private cleanupOldData(): void {
    const cutoff = new Date(Date.now() - MONITORING_CONFIG.METRICS_RETENTION_PERIOD)
    
    this.performanceData = this.performanceData.filter(d => d.timestamp >= cutoff)
    
    // Clean up evaluation times map
    for (const [linkId, times] of this.evaluationTimes.entries()) {
      if (times.length > 1000) {
        this.evaluationTimes.set(linkId, times.slice(-500))
      }
    }
    
    this.lastCleanup = new Date()
  }
}

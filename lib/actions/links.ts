'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { auth } from '@/auth'
import { LinkRepository } from '@/lib/repositories/link'
import { ProfileRepository } from '@/lib/repositories/profile'
import { createLinkSchema, updateLinkSchema, type CreateLinkData, type UpdateLinkData } from '@/lib/validations'
import { z } from 'zod'
import { handleServerActionError } from '@/lib/utils/error-logger'
import { ErrorFactory } from '@/lib/errors/app-errors'

// Server action result type
type ActionResult<T = any> = {
  success: boolean
  data?: T
  error?: string
}

/**
 * Create a new link
 */
export async function createLink(data: CreateLinkData): Promise<ActionResult> {
  let session: any = null
  let profile: any = null

  try {
    session = await auth()
    if (!session?.user?.id) {
      throw ErrorFactory.unauthorized()
    }

    // Get user's profile
    profile = await ProfileRepository.findByUserId(session.user.id)
    if (!profile) {
      throw ErrorFactory.profileNotFound()
    }

    // Validate the data
    const validatedData = createLinkSchema.parse(data)

    // Check link limit (example: max 50 links)
    const existingLinks = await LinkRepository.findByProfileId(profile.id)
    if (existingLinks.length >= 50) {
      throw ErrorFactory.linkLimitExceeded(existingLinks.length, 50)
    }

    // Create the link
    const link = await LinkRepository.create(profile.id, validatedData)

    revalidatePath('/dashboard/links')
    return { success: true, data: link }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      throw ErrorFactory.validationError(
        firstError?.path.join('.') || 'unknown',
        firstError?.message || 'Validation failed',
        data
      )
    }

    return handleServerActionError(error, 'create link', {
      userId: session?.user?.id,
      profileId: profile?.id,
      linkData: data
    })
  }
}

/**
 * Update an existing link
 */
export async function updateLink(id: string, data: UpdateLinkData): Promise<ActionResult> {
  let session: any = null
  let profile: any = null

  try {
    session = await auth()
    if (!session?.user?.id) {
      throw ErrorFactory.unauthorized()
    }

    // Get the link and verify ownership
    const link = await LinkRepository.findById(id)
    if (!link) {
      throw ErrorFactory.linkNotFound(id)
    }

    // Get user's profile to verify ownership
    profile = await ProfileRepository.findByUserId(session.user.id)
    if (!profile) {
      throw ErrorFactory.profileNotFound()
    }

    if (link.profileId !== profile.id) {
      throw ErrorFactory.forbidden({ reason: 'Link does not belong to user' })
    }

    // Validate the data
    const validatedData = updateLinkSchema.parse(data)

    // Update the link
    const updatedLink = await LinkRepository.update(id, validatedData)

    revalidatePath('/dashboard/links')
    return { success: true, data: updatedLink }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      throw ErrorFactory.validationError(
        firstError?.path.join('.') || 'unknown',
        firstError?.message || 'Validation failed',
        data
      )
    }

    return handleServerActionError(error, 'update link', {
      userId: session?.user?.id,
      profileId: profile?.id,
      linkId: id,
      linkData: data
    })
  }
}

/**
 * Delete a link
 */
export async function deleteLink(id: string): Promise<ActionResult> {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      throw ErrorFactory.unauthorized()
    }

    // Get the link and verify ownership
    const link = await LinkRepository.findById(id)
    if (!link) {
      throw ErrorFactory.linkNotFound(id)
    }

    // Get user's profile to verify ownership
    const profile = await ProfileRepository.findByUserId(session.user.id)
    if (!profile) {
      throw ErrorFactory.profileNotFound()
    }
    
    if (link.profileId !== profile.id) {
      throw ErrorFactory.forbidden({ reason: 'Link does not belong to user' })
    }

    // Delete the link
    await LinkRepository.delete(id)

    revalidatePath('/dashboard/links')
    return { success: true }
  } catch (error) {
    return handleServerActionError(error, 'delete link', {
      userId: session?.user?.id,
      profileId: profile?.id,
      linkId: id
    })
  }
}

/**
 * Toggle link visibility
 */
export async function toggleLinkVisibility(id: string): Promise<ActionResult> {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Get the link and verify ownership
    const link = await LinkRepository.findById(id)
    if (!link) {
      return { success: false, error: 'Link not found' }
    }

    // Get user's profile to verify ownership
    const profile = await ProfileRepository.findByUserId(session.user.id)
    if (!profile || link.profileId !== profile.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Toggle visibility
    const updatedLink = await LinkRepository.toggleVisibility(id)

    revalidatePath('/dashboard/links')
    return { success: true, data: updatedLink }
  } catch (error) {
    console.error('Error toggling link visibility:', error)
    return { success: false, error: 'Failed to toggle link visibility' }
  }
}

/**
 * Reorder links
 */
export async function reorderLinks(linkIds: string[]): Promise<ActionResult> {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Get user's profile
    const profile = await ProfileRepository.findByUserId(session.user.id)
    if (!profile) {
      return { success: false, error: 'Profile not found' }
    }

    // Reorder the links
    const updatedLinks = await LinkRepository.reorder(profile.id, linkIds)

    revalidatePath('/dashboard/links')
    return { success: true, data: updatedLinks }
  } catch (error) {
    console.error('Error reordering links:', error)
    return { success: false, error: 'Failed to reorder links' }
  }
}

/**
 * Get user's links
 */
export async function getUserLinks(): Promise<ActionResult> {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Get user's profile
    const profile = await ProfileRepository.findByUserId(session.user.id)
    if (!profile) {
      return { success: false, error: 'Profile not found' }
    }

    // Get all links (including hidden ones for management)
    const links = await LinkRepository.findByProfileId(profile.id, true)

    return { success: true, data: links }
  } catch (error) {
    console.error('Error getting user links:', error)
    return { success: false, error: 'Failed to get links' }
  }
}

/**
 * Duplicate a link
 */
export async function duplicateLink(id: string): Promise<ActionResult> {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Get the link and verify ownership
    const link = await LinkRepository.findById(id)
    if (!link) {
      return { success: false, error: 'Link not found' }
    }

    // Get user's profile to verify ownership
    const profile = await ProfileRepository.findByUserId(session.user.id)
    if (!profile || link.profileId !== profile.id) {
      return { success: false, error: 'Unauthorized' }
    }

    // Duplicate the link
    const duplicatedLink = await LinkRepository.duplicate(id)

    revalidatePath('/dashboard/links')
    return { success: true, data: duplicatedLink }
  } catch (error) {
    console.error('Error duplicating link:', error)
    return { success: false, error: 'Failed to duplicate link' }
  }
}
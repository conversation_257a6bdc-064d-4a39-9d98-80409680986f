/**
 * Robust timezone utility functions using dayjs for proper datetime handling
 */

import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc.js'
import timezone from 'dayjs/plugin/timezone.js'

// Configure dayjs with timezone support
dayjs.extend(utc)
dayjs.extend(timezone)

/**
 * Convert a stored UTC datetime to a datetime-local string for form display
 * @param storedDate - The UTC date from the database
 * @param timezone - The timezone the datetime should be displayed in (e.g., 'Asia/Kolkata')
 * @returns A datetime-local string (YYYY-MM-DDTHH:mm)
 */
export function formatDateTimeForInput(storedDate: Date, timezone: string): string {
  // Convert UTC date to the target timezone and format for datetime-local input
  return dayjs(storedDate).tz(timezone).format('YYYY-MM-DDTHH:mm')
}

/**
 * Convert a datetime-local string to a UTC Date object for database storage
 * @param dateTimeString - The datetime-local string from the form (YYYY-MM-DDTHH:mm)
 * @param timezone - The timezone the datetime should be interpreted in (e.g., 'Asia/Kolkata')
 * @returns A UTC Date object for database storage
 */
export function parseDateTimeFromInput(dateTimeString: string, timezone: string): Date {
  if (!dateTimeString) return new Date()

  // Parse the datetime-local string as being in the specified timezone
  // and convert to UTC for storage
  return dayjs.tz(dateTimeString, timezone).utc().toDate()
}

/**
 * Get the current time in a specific timezone
 * @param timezone - The target timezone (e.g., 'Asia/Kolkata')
 * @returns Current time as a Date object in that timezone
 */
export function getCurrentTimeInTimezone(timezone: string): Date {
  return dayjs().tz(timezone).toDate()
}

/**
 * Validate if a timezone string is valid IANA timezone
 * @param timezone - The timezone string to validate
 * @returns boolean indicating if the timezone is valid
 */
export function isValidTimezone(timezone: string): boolean {
  try {
    // dayjs will throw an error for invalid timezones
    dayjs().tz(timezone)
    return true
  } catch {
    return false
  }
}

/**
 * Format a date for display in a specific timezone
 * @param date - The date to format
 * @param timezone - The target timezone
 * @param format - The format string (default: 'YYYY-MM-DD HH:mm z')
 * @returns Formatted date string
 */
export function formatDateInTimezone(
  date: Date,
  timezone: string,
  format: string = 'YYYY-MM-DD HH:mm z'
): string {
  return dayjs(date).tz(timezone).format(format)
}

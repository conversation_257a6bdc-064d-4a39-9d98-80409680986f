import type { Link } from '@prisma/client'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc.js'
import timezone from 'dayjs/plugin/timezone.js'

// Configure dayjs with timezone support
dayjs.extend(utc)
dayjs.extend(timezone)

/**
 * Check if a link should be visible based on its scheduling configuration
 * This function properly handles timezone conversions for accurate scheduling using dayjs
 */
export function isLinkScheduleActive(link: Link, currentTime: Date = new Date()): boolean {
  // If not scheduled, use the regular visibility setting
  if (!link.isScheduled) {
    return link.isVisible
  }

  // If scheduled but no start time, treat as not active
  if (!link.scheduleStart) {
    return false
  }

  // Get the link's timezone (default to UTC if not specified)
  const linkTimezone = link.timezone || 'UTC'

  // Convert all times to the link's timezone for accurate comparison
  const currentTimeInLinkTz = dayjs(currentTime).tz(linkTimezone)
  const startTimeInLinkTz = dayjs(link.scheduleStart).tz(linkTimezone)

  // Check if current time is after start time
  if (currentTimeInLinkTz.isBefore(startTimeInLinkTz)) {
    return false
  }

  // Check if current time is before end time (if set)
  if (link.scheduleEnd) {
    const endTimeInLinkTz = dayjs(link.scheduleEnd).tz(linkTimezone)
    if (currentTimeInLinkTz.isAfter(endTimeInLinkTz)) {
      return false
    }
  }

  // Link is within schedule and visible
  return link.isVisible
}

/**
 * Filter links to only show those that are currently active based on scheduling
 */
export function filterActiveLinks(links: Link[], currentTime: Date = new Date()): Link[] {
  return links.filter(link => isLinkScheduleActive(link, currentTime))
}

/**
 * Get the next scheduled event for a link (start or end)
 */
export function getNextScheduleEvent(link: Link, currentTime: Date = new Date()): {
  type: 'start' | 'end' | null
  date: Date | null
  isActive: boolean
} {
  if (!link.isScheduled) {
    return { type: null, date: null, isActive: link.isVisible }
  }

  const isActive = isLinkScheduleActive(link, currentTime)
  const linkTimezone = link.timezone || 'UTC'
  const currentTimeInTz = dayjs(currentTime).tz(linkTimezone)

  // If not started yet, next event is start
  if (link.scheduleStart) {
    const startTimeInTz = dayjs(link.scheduleStart).tz(linkTimezone)
    if (currentTimeInTz.isBefore(startTimeInTz)) {
      return {
        type: 'start',
        date: link.scheduleStart,
        isActive: false
      }
    }
  }

  // If has end time and not ended yet, next event is end
  if (link.scheduleEnd) {
    const endTimeInTz = dayjs(link.scheduleEnd).tz(linkTimezone)
    if (currentTimeInTz.isBefore(endTimeInTz)) {
      return {
        type: 'end',
        date: link.scheduleEnd,
        isActive
      }
    }
  }

  // No upcoming events
  return { type: null, date: null, isActive }
}

/**
 * Format a schedule status for display
 */
export function formatScheduleStatus(link: Link, currentTime: Date = new Date()): string {
  if (!link.isScheduled) {
    return link.isVisible ? 'Always visible' : 'Hidden'
  }

  const nextEvent = getNextScheduleEvent(link, currentTime)
  const linkTimezone = link.timezone || 'UTC'

  if (nextEvent.type === 'start' && nextEvent.date) {
    const formattedDate = dayjs(nextEvent.date).tz(linkTimezone).format('MMM D, YYYY h:mm A z')
    return `Starts ${formattedDate}`
  }

  if (nextEvent.type === 'end' && nextEvent.date) {
    const formattedDate = dayjs(nextEvent.date).tz(linkTimezone).format('MMM D, YYYY h:mm A z')
    return `Ends ${formattedDate}`
  }

  if (nextEvent.isActive) {
    return 'Active (no end time)'
  }

  return 'Schedule ended'
}
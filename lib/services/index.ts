/**
 * Services Index
 * 
 * Central export point for all Rule Management Services
 */

// Core Rule Management Service
export { RuleManagementService } from './rule-management'
export type {
  RuleOperationResult,
  RuleLifecycleState,
  RuleManagementOptions,
  BulkRuleOperation,
  RuleTemplate,
  ServiceHealthStatus
} from './rule-management'

// Performance Dashboard Service
export { RulePerformanceDashboard } from './rule-performance-dashboard'
export type {
  DashboardMetrics,
  ReportConfig,
  PerformanceThresholds
} from './rule-performance-dashboard'

// Service Monitor
export { RuleServiceMonitor } from './rule-service-monitor'
export type {
  HealthCheckResult,
  MonitoringConfig,
  ServiceAlert,
  RecoveryAction
} from './rule-service-monitor'

/**
 * Service Factory
 * 
 * Factory class for creating and configuring rule management services
 */
export class RuleServiceFactory {
  /**
   * Create a fully configured Rule Management Service with monitoring
   */
  public static createManagedRuleService(options?: {
    ruleManagementOptions?: Parameters<typeof RuleManagementService.getInstance>[0]
    monitoringConfig?: Parameters<typeof RuleServiceMonitor.getInstance>[0]
    enableMonitoring?: boolean
  }) {
    const ruleService = RuleManagementService.getInstance(options?.ruleManagementOptions)
    const dashboard = RulePerformanceDashboard.getInstance()
    
    let monitor: RuleServiceMonitor | undefined
    if (options?.enableMonitoring !== false) {
      monitor = RuleServiceMonitor.getInstance(options?.monitoringConfig)
      monitor.startMonitoring()
    }

    return {
      ruleService,
      dashboard,
      monitor,
      
      /**
       * Get comprehensive service status
       */
      async getStatus() {
        const [serviceHealth, realTimeStatus, serviceStatus] = await Promise.all([
          ruleService.getServiceHealth(),
          dashboard.getRealTimeStatus(),
          monitor?.getServiceStatus()
        ])

        return {
          service: serviceHealth,
          realTime: realTimeStatus,
          monitoring: serviceStatus
        }
      },

      /**
       * Shutdown all services gracefully
       */
      shutdown() {
        monitor?.stopMonitoring()
        console.log('Rule Management Services shut down')
      }
    }
  }

  /**
   * Create a lightweight Rule Management Service without monitoring
   */
  public static createLightweightRuleService(
    options?: Parameters<typeof RuleManagementService.getInstance>[0]
  ) {
    return RuleManagementService.getInstance(options)
  }

  /**
   * Create just the performance dashboard
   */
  public static createPerformanceDashboard() {
    return RulePerformanceDashboard.getInstance()
  }

  /**
   * Create just the service monitor
   */
  public static createServiceMonitor(
    config?: Parameters<typeof RuleServiceMonitor.getInstance>[0]
  ) {
    return RuleServiceMonitor.getInstance(config)
  }
}

/**
 * Default service configuration
 */
export const DEFAULT_SERVICE_CONFIG = {
  ruleManagement: {
    autoResolveConflicts: true,
    enablePerformanceMonitoring: true,
    useTransactions: true,
    validationLevel: 'moderate' as const,
    enableOptimization: true,
    operationTimeout: 30000
  },
  monitoring: {
    healthCheckInterval: 30000,
    alertThresholds: {
      responseTime: 1000,
      errorRate: 0.05,
      memoryUsage: 0.8
    },
    autoRecovery: {
      enabled: true,
      maxAttempts: 3,
      backoffMultiplier: 2
    }
  }
} as const

/**
 * Service utilities
 */
export class RuleServiceUtils {
  /**
   * Validate service configuration
   */
  public static validateConfig(config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (config.ruleManagement) {
      if (config.ruleManagement.operationTimeout && config.ruleManagement.operationTimeout < 1000) {
        errors.push('Operation timeout must be at least 1000ms')
      }
      
      if (config.ruleManagement.validationLevel && 
          !['strict', 'moderate', 'lenient'].includes(config.ruleManagement.validationLevel)) {
        errors.push('Invalid validation level')
      }
    }

    if (config.monitoring) {
      if (config.monitoring.healthCheckInterval && config.monitoring.healthCheckInterval < 5000) {
        errors.push('Health check interval must be at least 5000ms')
      }
      
      if (config.monitoring.alertThresholds) {
        const thresholds = config.monitoring.alertThresholds
        if (thresholds.errorRate && (thresholds.errorRate < 0 || thresholds.errorRate > 1)) {
          errors.push('Error rate threshold must be between 0 and 1')
        }
        if (thresholds.memoryUsage && (thresholds.memoryUsage < 0 || thresholds.memoryUsage > 1)) {
          errors.push('Memory usage threshold must be between 0 and 1')
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Get recommended configuration based on environment
   */
  public static getRecommendedConfig(environment: 'development' | 'staging' | 'production') {
    const baseConfig = { ...DEFAULT_SERVICE_CONFIG }

    switch (environment) {
      case 'development':
        return {
          ...baseConfig,
          ruleManagement: {
            ...baseConfig.ruleManagement,
            validationLevel: 'lenient' as const,
            enableOptimization: false
          },
          monitoring: {
            ...baseConfig.monitoring,
            healthCheckInterval: 60000, // Less frequent in dev
            autoRecovery: {
              ...baseConfig.monitoring.autoRecovery,
              enabled: false // Disable auto-recovery in dev
            }
          }
        }

      case 'staging':
        return {
          ...baseConfig,
          ruleManagement: {
            ...baseConfig.ruleManagement,
            validationLevel: 'moderate' as const
          }
        }

      case 'production':
        return {
          ...baseConfig,
          ruleManagement: {
            ...baseConfig.ruleManagement,
            validationLevel: 'strict' as const,
            operationTimeout: 15000 // Stricter timeout in prod
          },
          monitoring: {
            ...baseConfig.monitoring,
            healthCheckInterval: 15000, // More frequent in prod
            alertThresholds: {
              ...baseConfig.monitoring.alertThresholds,
              responseTime: 500, // Stricter in prod
              errorRate: 0.02 // Lower tolerance in prod
            }
          }
        }

      default:
        return baseConfig
    }
  }

  /**
   * Create service configuration from environment variables
   */
  public static createConfigFromEnv(): typeof DEFAULT_SERVICE_CONFIG {
    return {
      ruleManagement: {
        autoResolveConflicts: process.env.RULE_AUTO_RESOLVE_CONFLICTS !== 'false',
        enablePerformanceMonitoring: process.env.RULE_PERFORMANCE_MONITORING !== 'false',
        useTransactions: process.env.RULE_USE_TRANSACTIONS !== 'false',
        validationLevel: (process.env.RULE_VALIDATION_LEVEL as any) || 'moderate',
        enableOptimization: process.env.RULE_ENABLE_OPTIMIZATION !== 'false',
        operationTimeout: parseInt(process.env.RULE_OPERATION_TIMEOUT || '30000')
      },
      monitoring: {
        healthCheckInterval: parseInt(process.env.RULE_HEALTH_CHECK_INTERVAL || '30000'),
        alertThresholds: {
          responseTime: parseInt(process.env.RULE_ALERT_RESPONSE_TIME || '1000'),
          errorRate: parseFloat(process.env.RULE_ALERT_ERROR_RATE || '0.05'),
          memoryUsage: parseFloat(process.env.RULE_ALERT_MEMORY_USAGE || '0.8')
        },
        autoRecovery: {
          enabled: process.env.RULE_AUTO_RECOVERY !== 'false',
          maxAttempts: parseInt(process.env.RULE_RECOVERY_MAX_ATTEMPTS || '3'),
          backoffMultiplier: parseInt(process.env.RULE_RECOVERY_BACKOFF || '2')
        }
      }
    }
  }
}

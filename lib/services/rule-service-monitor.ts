/**
 * Rule Service Monitor
 * 
 * Comprehensive monitoring and health checking service for the Rule Management Service.
 * Provides continuous monitoring, alerting, and automated recovery capabilities.
 */

import { RuleManagementService } from './rule-management'
import { RulePerformanceDashboard } from './rule-performance-dashboard'
import { db } from '@/lib/db'

/**
 * Service health check result
 */
export interface HealthCheckResult {
  service: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: Date
  responseTime: number
  details: {
    database: 'connected' | 'disconnected' | 'slow'
    ruleEngine: 'operational' | 'degraded' | 'failed'
    cache: 'optimal' | 'suboptimal' | 'failed'
    memory: 'normal' | 'high' | 'critical'
  }
  metrics: {
    totalOperations: number
    successRate: number
    averageResponseTime: number
    errorRate: number
  }
  issues: string[]
  recommendations: string[]
}

/**
 * Monitoring configuration
 */
export interface MonitoringConfig {
  healthCheckInterval: number // milliseconds
  alertThresholds: {
    responseTime: number
    errorRate: number
    memoryUsage: number
  }
  autoRecovery: {
    enabled: boolean
    maxAttempts: number
    backoffMultiplier: number
  }
  notifications: {
    email?: string[]
    webhook?: string
    slack?: string
  }
}

/**
 * Service alert
 */
export interface ServiceAlert {
  id: string
  type: 'performance' | 'availability' | 'error' | 'resource'
  severity: 'info' | 'warning' | 'critical'
  title: string
  description: string
  timestamp: Date
  service: string
  metrics?: Record<string, number>
  resolved: boolean
  resolvedAt?: Date
}

/**
 * Recovery action
 */
export interface RecoveryAction {
  id: string
  type: 'restart_service' | 'clear_cache' | 'reduce_load' | 'scale_up'
  description: string
  automated: boolean
  success: boolean
  timestamp: Date
  details?: Record<string, any>
}

/**
 * Rule Service Monitor
 */
export class RuleServiceMonitor {
  private static instance: RuleServiceMonitor
  private ruleManagementService: RuleManagementService
  private performanceDashboard: RulePerformanceDashboard
  private config: MonitoringConfig
  private healthCheckTimer?: NodeJS.Timeout
  private alerts: Map<string, ServiceAlert> = new Map()
  private recoveryAttempts: Map<string, number> = new Map()
  private alertSubscribers: Array<(alert: ServiceAlert) => void> = []

  private constructor(config?: Partial<MonitoringConfig>) {
    this.ruleManagementService = RuleManagementService.getInstance()
    this.performanceDashboard = RulePerformanceDashboard.getInstance()
    
    // Default configuration
    this.config = {
      healthCheckInterval: 30000, // 30 seconds
      alertThresholds: {
        responseTime: 1000, // 1 second
        errorRate: 0.05, // 5%
        memoryUsage: 0.8 // 80%
      },
      autoRecovery: {
        enabled: true,
        maxAttempts: 3,
        backoffMultiplier: 2
      },
      notifications: {},
      ...config
    }

    this.setupMonitoring()
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<MonitoringConfig>): RuleServiceMonitor {
    if (!RuleServiceMonitor.instance) {
      RuleServiceMonitor.instance = new RuleServiceMonitor(config)
    }
    return RuleServiceMonitor.instance
  }

  /**
   * Start continuous monitoring
   */
  public startMonitoring(): void {
    if (this.healthCheckTimer) {
      this.stopMonitoring()
    }

    console.log('Starting Rule Service monitoring...')
    
    // Perform initial health check
    this.performHealthCheck()
    
    // Schedule periodic health checks
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck()
    }, this.config.healthCheckInterval)
  }

  /**
   * Stop monitoring
   */
  public stopMonitoring(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer)
      this.healthCheckTimer = undefined
      console.log('Rule Service monitoring stopped')
    }
  }

  /**
   * Perform comprehensive health check
   */
  public async performHealthCheck(): Promise<HealthCheckResult> {
    const startTime = performance.now()
    const timestamp = new Date()
    
    try {
      // Check database connectivity
      const dbStatus = await this.checkDatabaseHealth()
      
      // Check rule engine status
      const ruleEngineStatus = await this.checkRuleEngineHealth()
      
      // Check cache performance
      const cacheStatus = await this.checkCacheHealth()
      
      // Check memory usage
      const memoryStatus = this.checkMemoryHealth()
      
      // Get service metrics
      const serviceHealth = this.ruleManagementService.getServiceHealth()
      const performanceStats = this.ruleManagementService.getPerformanceStats()
      
      const responseTime = performance.now() - startTime
      
      // Determine overall status
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
      const issues: string[] = []
      const recommendations: string[] = []
      
      if (dbStatus !== 'connected' || ruleEngineStatus === 'failed') {
        status = 'unhealthy'
        issues.push('Critical service components unavailable')
      } else if (dbStatus === 'slow' || ruleEngineStatus === 'degraded' || 
                 cacheStatus === 'suboptimal' || memoryStatus === 'high') {
        status = 'degraded'
        issues.push('Service performance degraded')
      }
      
      // Check against thresholds
      if (responseTime > this.config.alertThresholds.responseTime) {
        if (status === 'healthy') status = 'degraded'
        issues.push('Health check response time exceeded threshold')
        recommendations.push('Investigate performance bottlenecks')
      }
      
      if (performanceStats.errorRate > this.config.alertThresholds.errorRate) {
        if (status === 'healthy') status = 'degraded'
        issues.push('Error rate above threshold')
        recommendations.push('Review recent rule changes and configurations')
      }
      
      const healthResult: HealthCheckResult = {
        service: 'RuleManagementService',
        status,
        timestamp,
        responseTime,
        details: {
          database: dbStatus,
          ruleEngine: ruleEngineStatus,
          cache: cacheStatus,
          memory: memoryStatus
        },
        metrics: {
          totalOperations: performanceStats.totalOperations,
          successRate: 1 - (performanceStats.errorRate / 100),
          averageResponseTime: performanceStats.averageResponseTime,
          errorRate: performanceStats.errorRate
        },
        issues,
        recommendations
      }
      
      // Process health check results
      await this.processHealthCheckResult(healthResult)
      
      return healthResult
      
    } catch (error) {
      const responseTime = performance.now() - startTime
      
      const healthResult: HealthCheckResult = {
        service: 'RuleManagementService',
        status: 'unhealthy',
        timestamp,
        responseTime,
        details: {
          database: 'disconnected',
          ruleEngine: 'failed',
          cache: 'failed',
          memory: 'unknown'
        },
        metrics: {
          totalOperations: 0,
          successRate: 0,
          averageResponseTime: 0,
          errorRate: 1
        },
        issues: [`Health check failed: ${(error as Error).message}`],
        recommendations: ['Investigate service connectivity and configuration']
      }
      
      await this.processHealthCheckResult(healthResult)
      return healthResult
    }
  }

  /**
   * Get current service status
   */
  public async getServiceStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    uptime: number
    lastHealthCheck: Date
    activeAlerts: ServiceAlert[]
    recentRecoveryActions: RecoveryAction[]
  }> {
    const serviceHealth = this.ruleManagementService.getServiceHealth()
    const activeAlerts = Array.from(this.alerts.values()).filter(a => !a.resolved)
    
    return {
      status: serviceHealth.status,
      uptime: serviceHealth.uptime,
      lastHealthCheck: serviceHealth.lastHealthCheck,
      activeAlerts,
      recentRecoveryActions: [] // Would be stored in database
    }
  }

  /**
   * Subscribe to service alerts
   */
  public onAlert(callback: (alert: ServiceAlert) => void): void {
    this.alertSubscribers.push(callback)
  }

  /**
   * Manually trigger recovery action
   */
  public async triggerRecovery(actionType: RecoveryAction['type']): Promise<RecoveryAction> {
    const recoveryAction: RecoveryAction = {
      id: `recovery_${Date.now()}`,
      type: actionType,
      description: this.getRecoveryActionDescription(actionType),
      automated: false,
      success: false,
      timestamp: new Date()
    }

    try {
      switch (actionType) {
        case 'clear_cache':
          this.ruleManagementService.clearCache()
          recoveryAction.success = true
          break
          
        case 'restart_service':
          this.ruleManagementService.resetStats()
          recoveryAction.success = true
          break
          
        case 'reduce_load':
          // Implementation would depend on load balancing setup
          recoveryAction.success = true
          break
          
        case 'scale_up':
          // Implementation would depend on infrastructure setup
          recoveryAction.success = true
          break
      }
    } catch (error) {
      recoveryAction.details = { error: (error as Error).message }
    }

    console.log(`Recovery action ${actionType} ${recoveryAction.success ? 'succeeded' : 'failed'}`)
    return recoveryAction
  }

  // ===== PRIVATE METHODS =====

  private setupMonitoring(): void {
    // Subscribe to performance alerts
    this.performanceDashboard.onAlert((alert) => {
      this.handlePerformanceAlert(alert)
    })
  }

  private async checkDatabaseHealth(): Promise<'connected' | 'disconnected' | 'slow'> {
    try {
      const startTime = performance.now()
      await db.$queryRaw`SELECT 1`
      const queryTime = performance.now() - startTime
      
      if (queryTime > 1000) {
        return 'slow'
      }
      return 'connected'
    } catch (error) {
      return 'disconnected'
    }
  }

  private async checkRuleEngineHealth(): Promise<'operational' | 'degraded' | 'failed'> {
    try {
      const realTimeStatus = await this.performanceDashboard.getRealTimeStatus()
      
      if (realTimeStatus.status === 'critical') {
        return 'failed'
      } else if (realTimeStatus.status === 'warning') {
        return 'degraded'
      }
      return 'operational'
    } catch (error) {
      return 'failed'
    }
  }

  private async checkCacheHealth(): Promise<'optimal' | 'suboptimal' | 'failed'> {
    try {
      const cacheStats = this.ruleManagementService.getCacheStats()
      
      if (cacheStats.hitRate > 0.8) {
        return 'optimal'
      } else if (cacheStats.hitRate > 0.5) {
        return 'suboptimal'
      }
      return 'failed'
    } catch (error) {
      return 'failed'
    }
  }

  private checkMemoryHealth(): 'normal' | 'high' | 'critical' {
    try {
      const memoryUsage = process.memoryUsage()
      const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024
      const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024
      const usageRatio = heapUsedMB / heapTotalMB
      
      if (usageRatio > 0.9) {
        return 'critical'
      } else if (usageRatio > 0.7) {
        return 'high'
      }
      return 'normal'
    } catch (error) {
      return 'critical'
    }
  }

  private async processHealthCheckResult(result: HealthCheckResult): Promise<void> {
    // Generate alerts based on health check results
    if (result.status === 'unhealthy') {
      await this.createAlert({
        type: 'availability',
        severity: 'critical',
        title: 'Service Unhealthy',
        description: `Rule Management Service is unhealthy: ${result.issues.join(', ')}`,
        service: result.service,
        metrics: result.metrics
      })
    } else if (result.status === 'degraded') {
      await this.createAlert({
        type: 'performance',
        severity: 'warning',
        title: 'Service Degraded',
        description: `Rule Management Service performance degraded: ${result.issues.join(', ')}`,
        service: result.service,
        metrics: result.metrics
      })
    }

    // Trigger auto-recovery if enabled
    if (this.config.autoRecovery.enabled && result.status !== 'healthy') {
      await this.attemptAutoRecovery(result)
    }
  }

  private async createAlert(alertData: Omit<ServiceAlert, 'id' | 'timestamp' | 'resolved'>): Promise<void> {
    const alert: ServiceAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      resolved: false,
      ...alertData
    }

    this.alerts.set(alert.id, alert)
    
    // Notify subscribers
    this.alertSubscribers.forEach(callback => callback(alert))
    
    console.log(`Alert created: ${alert.title} (${alert.severity})`)
  }

  private async handlePerformanceAlert(performanceAlert: any): Promise<void> {
    await this.createAlert({
      type: 'performance',
      severity: performanceAlert.severity === 'critical' ? 'critical' : 'warning',
      title: `Performance Alert: ${performanceAlert.type}`,
      description: `Performance threshold exceeded for ${performanceAlert.type}`,
      service: 'RuleManagementService',
      metrics: { [performanceAlert.type]: performanceAlert.actualValue }
    })
  }

  private async attemptAutoRecovery(healthResult: HealthCheckResult): Promise<void> {
    const recoveryKey = `${healthResult.service}_${healthResult.status}`
    const attempts = this.recoveryAttempts.get(recoveryKey) || 0
    
    if (attempts >= this.config.autoRecovery.maxAttempts) {
      console.log(`Max recovery attempts reached for ${recoveryKey}`)
      return
    }

    this.recoveryAttempts.set(recoveryKey, attempts + 1)
    
    // Determine recovery action based on health issues
    let recoveryAction: RecoveryAction['type'] = 'clear_cache'
    
    if (healthResult.details.database === 'disconnected') {
      recoveryAction = 'restart_service'
    } else if (healthResult.details.memory === 'critical') {
      recoveryAction = 'reduce_load'
    } else if (healthResult.responseTime > this.config.alertThresholds.responseTime * 2) {
      recoveryAction = 'scale_up'
    }

    console.log(`Attempting auto-recovery: ${recoveryAction} (attempt ${attempts + 1})`)
    
    const recovery = await this.triggerRecovery(recoveryAction)
    
    if (recovery.success) {
      // Reset recovery attempts on success
      this.recoveryAttempts.delete(recoveryKey)
    } else {
      // Exponential backoff
      const delay = 1000 * Math.pow(this.config.autoRecovery.backoffMultiplier, attempts)
      setTimeout(() => {
        this.attemptAutoRecovery(healthResult)
      }, delay)
    }
  }

  private getRecoveryActionDescription(actionType: RecoveryAction['type']): string {
    switch (actionType) {
      case 'clear_cache':
        return 'Clear rule evaluation cache to free memory and reset performance'
      case 'restart_service':
        return 'Reset service statistics and clear internal state'
      case 'reduce_load':
        return 'Temporarily reduce processing load to improve performance'
      case 'scale_up':
        return 'Scale up service resources to handle increased load'
      default:
        return 'Unknown recovery action'
    }
  }
}

# Rule Management Services Test Suite

This directory contains comprehensive tests for the Rule Management Services implementation. The test suite includes unit tests, integration tests, and performance tests to ensure the reliability and maintainability of the service layer.

## Test Structure

```
lib/services/__tests__/
├── rule-management.test.ts          # Unit tests for RuleManagementService
├── rule-performance-dashboard.test.ts # Unit tests for RulePerformanceDashboard
├── rule-service-monitor.test.ts     # Unit tests for RuleServiceMonitor
├── integration.test.ts              # Integration tests for all services
├── test-utils.ts                    # Test utilities and mock data factories
├── jest.config.js                   # Jest configuration
├── jest.setup.ts                    # Jest setup and custom matchers
├── run-tests.sh                     # Test runner script
└── README.md                        # This file
```

## Running Tests

### Prerequisites

- Node.js 18+ installed
- Bun or npm package manager
- Dependencies installed (`bun install` or `npm install`)

### Quick Start

```bash
# Run all tests
./run-tests.sh

# Or using bun/npm directly
bun test
npm test
```

### Test Commands

```bash
# Run specific test suites
./run-tests.sh unit          # Unit tests only
./run-tests.sh integration   # Integration tests only
./run-tests.sh coverage      # Tests with coverage report
./run-tests.sh watch         # Watch mode for development
./run-tests.sh ci            # CI mode (no watch, with coverage)
./run-tests.sh performance   # Performance tests only

# Run individual test files
bun test rule-management.test.ts
bun test rule-performance-dashboard.test.ts
bun test rule-service-monitor.test.ts
bun test integration.test.ts
```

## Test Categories

### 1. Unit Tests

**File: `rule-management.test.ts`**
- Tests for RuleManagementService core functionality
- Rule CRUD operations (create, read, update, delete)
- Validation and conflict resolution
- Bulk operations and transactions
- Rule lifecycle management
- Error handling and edge cases

**File: `rule-performance-dashboard.test.ts`**
- Tests for RulePerformanceDashboard functionality
- Dashboard metrics generation
- Performance report creation
- Real-time status monitoring
- Alert handling and thresholds
- Data export capabilities

**File: `rule-service-monitor.test.ts`**
- Tests for RuleServiceMonitor functionality
- Health check operations
- Monitoring lifecycle (start/stop)
- Alert generation and notification
- Recovery action execution
- Auto-recovery mechanisms

### 2. Integration Tests

**File: `integration.test.ts`**
- Tests for service interactions and workflows
- End-to-end rule management scenarios
- Service factory functionality
- Performance monitoring integration
- Auto-recovery integration
- Configuration management
- Service lifecycle management

### 3. Test Utilities

**File: `test-utils.ts`**
- Mock data factories for consistent test data
- Service health mock data generators
- Test assertion helpers
- Performance testing utilities
- Environment setup and cleanup utilities

## Test Coverage

The test suite aims for comprehensive coverage:

- **Lines**: 80%+ coverage target
- **Functions**: 80%+ coverage target
- **Branches**: 80%+ coverage target
- **Statements**: 80%+ coverage target

### Coverage Reports

```bash
# Generate coverage report
./run-tests.sh coverage

# View coverage report
open coverage/lcov-report/index.html  # macOS
xdg-open coverage/lcov-report/index.html  # Linux
```

Coverage reports are generated in multiple formats:
- **HTML**: `coverage/lcov-report/index.html` (interactive browser report)
- **LCOV**: `coverage/lcov.info` (for CI/CD integration)
- **JSON**: `coverage/coverage-final.json` (programmatic access)
- **Text**: Console output during test run

## Mock Strategy

The tests use comprehensive mocking to isolate units under test:

### External Dependencies
- **Database (`@/lib/db`)**: Mocked to prevent actual database operations
- **Repositories**: Mocked to control data layer responses
- **Rule Engine Components**: Mocked to focus on service layer logic

### Internal Dependencies
- **Service Instances**: Singleton instances are reset between tests
- **Timers**: Fake timers for testing time-dependent functionality
- **Performance APIs**: Mocked for consistent performance measurements

### Mock Data
- **TestDataFactory**: Creates consistent mock rule and link data
- **MockServiceData**: Generates realistic service health and performance data
- **Configurable Mocks**: Easy to customize for specific test scenarios

## Custom Matchers

The test suite includes custom Jest matchers for better assertions:

```typescript
// Performance metrics validation
expect(result.performanceMetrics).toHaveValidPerformanceMetrics()

// Service health validation
expect(serviceHealth).toHaveValidServiceHealth()

// Numeric range validation
expect(responseTime).toBeWithinRange(0, 1000)
```

## Test Data Management

### Mock Data Factories

```typescript
import { TestDataFactory } from './test-utils'

// Create mock rules
const referrerRule = TestDataFactory.createMockReferrerRule()
const locationRule = TestDataFactory.createMockLocationRule()
const multipleRules = TestDataFactory.createMockRules(5)

// Create mock links
const mockLink = TestDataFactory.createMockLink({
  id: 'custom-link-id',
  title: 'Custom Link'
})
```

### Service Health Mocks

```typescript
import { MockServiceData } from './test-utils'

// Create different health states
const healthyService = MockServiceData.createHealthyServiceHealth()
const degradedService = MockServiceData.createDegradedServiceHealth()
const unhealthyService = MockServiceData.createUnhealthyServiceHealth()
```

## Performance Testing

### Execution Time Measurement

```typescript
import { PerformanceTestUtils } from './test-utils'

// Measure single operation
const { result, executionTime } = await PerformanceTestUtils.measureExecutionTime(
  () => ruleService.createRule(linkId, ruleData)
)

// Benchmark multiple iterations
const benchmark = await PerformanceTestUtils.benchmarkFunction(
  () => ruleService.createRule(linkId, ruleData),
  10 // iterations
)

console.log(`Average time: ${benchmark.averageTime}ms`)
```

### Performance Assertions

```typescript
// Assert execution time is within limits
PerformanceTestUtils.assertExecutionTime(executionTime, 1000, 'Rule creation')

// Assert performance metrics are valid
TestAssertions.assertPerformanceMetrics(result.performanceMetrics, 500)
```

## Debugging Tests

### Common Issues and Solutions

1. **Test Timeouts**
   ```bash
   # Increase timeout for specific tests
   jest.setTimeout(60000)
   
   # Or run with longer timeout
   bun test --testTimeout=60000
   ```

2. **Mock Issues**
   ```typescript
   // Clear mocks between tests
   beforeEach(() => {
     jest.clearAllMocks()
   })
   
   // Reset modules for singleton cleanup
   beforeEach(() => {
     jest.resetModules()
   })
   ```

3. **Async Operation Issues**
   ```typescript
   // Wait for async operations
   await TestEnvironment.waitForAsyncOperations(100)
   
   // Use fake timers for time-dependent tests
   jest.useFakeTimers()
   jest.advanceTimersByTime(1000)
   ```

### Debug Mode

```bash
# Run tests with verbose output
bun test --verbose

# Run specific test with debugging
bun test --testNamePattern="should create rule successfully" --verbose

# Run tests in watch mode for development
./run-tests.sh watch
```

## CI/CD Integration

### GitHub Actions Example

```yaml
name: Test Rule Management Services

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: oven-sh/setup-bun@v1
      - run: bun install
      - run: cd lib/services/__tests__ && ./run-tests.sh ci
      - uses: codecov/codecov-action@v3
        with:
          file: ./lib/services/__tests__/coverage/lcov.info
```

### Test Scripts in package.json

```json
{
  "scripts": {
    "test:services": "cd lib/services/__tests__ && ./run-tests.sh",
    "test:services:unit": "cd lib/services/__tests__ && ./run-tests.sh unit",
    "test:services:integration": "cd lib/services/__tests__ && ./run-tests.sh integration",
    "test:services:coverage": "cd lib/services/__tests__ && ./run-tests.sh coverage",
    "test:services:watch": "cd lib/services/__tests__ && ./run-tests.sh watch"
  }
}
```

## Best Practices

### Writing Tests

1. **Descriptive Test Names**: Use clear, descriptive test names that explain what is being tested
2. **Arrange-Act-Assert**: Structure tests with clear setup, execution, and assertion phases
3. **Single Responsibility**: Each test should focus on one specific behavior
4. **Mock External Dependencies**: Mock all external dependencies to isolate the unit under test
5. **Test Edge Cases**: Include tests for error conditions and edge cases

### Test Organization

1. **Group Related Tests**: Use `describe` blocks to group related test cases
2. **Setup and Teardown**: Use `beforeEach`/`afterEach` for consistent test setup
3. **Shared Test Data**: Use factories for consistent test data creation
4. **Custom Matchers**: Create custom matchers for domain-specific assertions

### Performance Considerations

1. **Parallel Execution**: Tests run in parallel by default for faster execution
2. **Mock Heavy Operations**: Mock database operations and external API calls
3. **Cleanup Resources**: Properly cleanup timers, intervals, and event listeners
4. **Optimize Test Data**: Use minimal test data that still validates the behavior

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Add appropriate mocks for new dependencies
3. Update test utilities if adding reusable test functionality
4. Ensure new tests maintain the coverage thresholds
5. Add documentation for complex test scenarios

## Troubleshooting

### Common Test Failures

1. **Singleton State Issues**: Ensure singletons are reset between tests
2. **Timer Issues**: Use fake timers for time-dependent functionality
3. **Mock Configuration**: Verify mocks are properly configured for test scenarios
4. **Async Issues**: Ensure all async operations are properly awaited

### Getting Help

1. Check the test output for specific error messages
2. Run individual test files to isolate issues
3. Use the debugging techniques outlined above
4. Review the test utilities and mock configurations

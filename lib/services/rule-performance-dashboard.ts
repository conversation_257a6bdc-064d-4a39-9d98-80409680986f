/**
 * Rule Performance Dashboard Service
 * 
 * Provides comprehensive performance monitoring and analytics for the Rule Management Service.
 * This service aggregates performance data, generates reports, and provides real-time monitoring
 * capabilities for rule evaluation performance.
 */

import { RuleManagementService } from './rule-management'
import { RulePerformanceMonitor, type PerformanceAlert, type PerformanceStats } from '@/lib/rule-engine'
import { LinkConditionRepository } from '@/lib/repositories/link-condition'
import { db } from '@/lib/db'

/**
 * Performance dashboard metrics
 */
export interface DashboardMetrics {
  overview: {
    totalRules: number
    activeRules: number
    averageEvaluationTime: number
    successRate: number
    errorRate: number
    cacheHitRate: number
  }
  trends: {
    evaluationTimes: Array<{ timestamp: Date; value: number }>
    errorRates: Array<{ timestamp: Date; value: number }>
    throughput: Array<{ timestamp: Date; value: number }>
  }
  topPerformers: {
    fastestRules: Array<{ ruleId: string; averageTime: number }>
    slowestRules: Array<{ ruleId: string; averageTime: number }>
    mostUsedRules: Array<{ ruleId: string; usageCount: number }>
  }
  alerts: PerformanceAlert[]
  recommendations: string[]
}

/**
 * Performance report configuration
 */
export interface ReportConfig {
  timeRange: {
    start: Date
    end: Date
  }
  includeDetails: boolean
  groupBy: 'hour' | 'day' | 'week' | 'month'
  metrics: Array<'evaluation_time' | 'error_rate' | 'cache_hit_rate' | 'throughput'>
}

/**
 * Performance threshold configuration
 */
export interface PerformanceThresholds {
  evaluationTime: {
    warning: number
    critical: number
  }
  errorRate: {
    warning: number
    critical: number
  }
  cacheHitRate: {
    warning: number
    critical: number
  }
}

/**
 * Rule Performance Dashboard Service
 */
export class RulePerformanceDashboard {
  private static instance: RulePerformanceDashboard
  private ruleManagementService: RuleManagementService
  private performanceMonitor: RulePerformanceMonitor
  private alertSubscribers: Array<(alert: PerformanceAlert) => void> = []
  private thresholds: PerformanceThresholds

  private constructor() {
    this.ruleManagementService = RuleManagementService.getInstance()
    this.performanceMonitor = RulePerformanceMonitor.getInstance()
    
    // Default thresholds
    this.thresholds = {
      evaluationTime: { warning: 100, critical: 500 },
      errorRate: { warning: 0.05, critical: 0.1 },
      cacheHitRate: { warning: 0.7, critical: 0.5 }
    }

    this.setupAlertHandling()
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): RulePerformanceDashboard {
    if (!RulePerformanceDashboard.instance) {
      RulePerformanceDashboard.instance = new RulePerformanceDashboard()
    }
    return RulePerformanceDashboard.instance
  }

  /**
   * Get comprehensive dashboard metrics
   */
  public async getDashboardMetrics(timeRange?: { start: Date; end: Date }): Promise<DashboardMetrics> {
    const [overview, trends, topPerformers, alerts] = await Promise.all([
      this.getOverviewMetrics(timeRange),
      this.getTrendMetrics(timeRange),
      this.getTopPerformers(timeRange),
      this.getActiveAlerts()
    ])

    const recommendations = await this.generateRecommendations(overview)

    return {
      overview,
      trends,
      topPerformers,
      alerts,
      recommendations
    }
  }

  /**
   * Generate performance report
   */
  public async generatePerformanceReport(config: ReportConfig): Promise<{
    summary: {
      totalEvaluations: number
      averageEvaluationTime: number
      errorRate: number
      cacheHitRate: number
      peakThroughput: number
    }
    details: Array<{
      timestamp: Date
      metrics: Record<string, number>
    }>
    insights: string[]
    recommendations: string[]
  }> {
    const startTime = performance.now()

    // Fetch performance data for the specified time range
    const performanceData = await this.fetchPerformanceData(config.timeRange, config.groupBy)
    
    // Calculate summary metrics
    const summary = this.calculateSummaryMetrics(performanceData)
    
    // Generate detailed metrics based on configuration
    const details = this.generateDetailedMetrics(performanceData, config)
    
    // Generate insights and recommendations
    const insights = this.generateInsights(performanceData, summary)
    const recommendations = await this.generateRecommendations(summary)

    const reportGenerationTime = performance.now() - startTime
    console.log(`Performance report generated in ${reportGenerationTime.toFixed(2)}ms`)

    return {
      summary,
      details,
      insights,
      recommendations
    }
  }

  /**
   * Set performance thresholds
   */
  public setThresholds(thresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds }
  }

  /**
   * Subscribe to performance alerts
   */
  public onAlert(callback: (alert: PerformanceAlert) => void): void {
    this.alertSubscribers.push(callback)
  }

  /**
   * Get real-time performance status
   */
  public async getRealTimeStatus(): Promise<{
    status: 'healthy' | 'warning' | 'critical'
    currentMetrics: {
      evaluationTime: number
      errorRate: number
      cacheHitRate: number
      throughput: number
    }
    activeIssues: string[]
    uptime: number
  }> {
    const serviceHealth = this.ruleManagementService.getServiceHealth()
    const performanceStats = this.ruleManagementService.getPerformanceStats()
    
    // Determine overall status
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'
    const activeIssues: string[] = []

    // Check evaluation time
    if (performanceStats.averageEvaluationTime > this.thresholds.evaluationTime.critical) {
      status = 'critical'
      activeIssues.push('Critical: Evaluation time exceeds threshold')
    } else if (performanceStats.averageEvaluationTime > this.thresholds.evaluationTime.warning) {
      if (status === 'healthy') status = 'warning'
      activeIssues.push('Warning: Evaluation time above normal')
    }

    // Check error rate
    if (performanceStats.errorRate > this.thresholds.errorRate.critical) {
      status = 'critical'
      activeIssues.push('Critical: Error rate too high')
    } else if (performanceStats.errorRate > this.thresholds.errorRate.warning) {
      if (status === 'healthy') status = 'warning'
      activeIssues.push('Warning: Error rate elevated')
    }

    // Check cache hit rate
    if (performanceStats.cacheHitRate < this.thresholds.cacheHitRate.critical) {
      status = 'critical'
      activeIssues.push('Critical: Cache hit rate too low')
    } else if (performanceStats.cacheHitRate < this.thresholds.cacheHitRate.warning) {
      if (status === 'healthy') status = 'warning'
      activeIssues.push('Warning: Cache hit rate below optimal')
    }

    return {
      status,
      currentMetrics: {
        evaluationTime: performanceStats.averageEvaluationTime,
        errorRate: performanceStats.errorRate,
        cacheHitRate: performanceStats.cacheHitRate,
        throughput: performanceStats.totalOperations / (serviceHealth.uptime / 1000)
      },
      activeIssues,
      uptime: serviceHealth.uptime
    }
  }

  /**
   * Export performance data
   */
  public async exportPerformanceData(
    format: 'json' | 'csv',
    timeRange: { start: Date; end: Date }
  ): Promise<string> {
    const data = await this.fetchPerformanceData(timeRange, 'hour')
    
    if (format === 'json') {
      return JSON.stringify(data, null, 2)
    } else {
      // Convert to CSV format
      const headers = ['timestamp', 'evaluationTime', 'errorRate', 'cacheHitRate', 'throughput']
      const csvRows = [headers.join(',')]
      
      for (const row of data) {
        const values = [
          row.timestamp.toISOString(),
          row.evaluationTime.toString(),
          row.errorRate.toString(),
          row.cacheHitRate.toString(),
          row.throughput.toString()
        ]
        csvRows.push(values.join(','))
      }
      
      return csvRows.join('\n')
    }
  }

  // ===== PRIVATE METHODS =====

  private setupAlertHandling(): void {
    this.performanceMonitor.onAlert((alert) => {
      // Forward alerts to subscribers
      this.alertSubscribers.forEach(callback => callback(alert))
      
      // Log critical alerts
      if (alert.severity === 'critical') {
        console.error('Critical performance alert:', alert)
      }
    })
  }

  private async getOverviewMetrics(timeRange?: { start: Date; end: Date }): Promise<DashboardMetrics['overview']> {
    const [totalRules, activeRules] = await Promise.all([
      LinkConditionRepository.count(),
      LinkConditionRepository.countActive()
    ])

    const performanceStats = this.performanceMonitor.getPerformanceStats()
    
    return {
      totalRules,
      activeRules,
      averageEvaluationTime: performanceStats.averageEvaluationTime,
      successRate: 1 - (performanceStats.errorRate / 100),
      errorRate: performanceStats.errorRate,
      cacheHitRate: performanceStats.cacheHitRate
    }
  }

  private async getTrendMetrics(timeRange?: { start: Date; end: Date }): Promise<DashboardMetrics['trends']> {
    // This would typically fetch from a time-series database
    // For now, return mock data structure
    const now = new Date()
    const hoursBack = 24
    
    const evaluationTimes: Array<{ timestamp: Date; value: number }> = []
    const errorRates: Array<{ timestamp: Date; value: number }> = []
    const throughput: Array<{ timestamp: Date; value: number }> = []
    
    for (let i = hoursBack; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000)
      evaluationTimes.push({ timestamp, value: Math.random() * 100 + 50 })
      errorRates.push({ timestamp, value: Math.random() * 0.05 })
      throughput.push({ timestamp, value: Math.random() * 1000 + 500 })
    }
    
    return {
      evaluationTimes,
      errorRates,
      throughput
    }
  }

  private async getTopPerformers(timeRange?: { start: Date; end: Date }): Promise<DashboardMetrics['topPerformers']> {
    // This would typically query performance data from database
    // For now, return mock data
    return {
      fastestRules: [
        { ruleId: 'rule-1', averageTime: 25 },
        { ruleId: 'rule-2', averageTime: 30 },
        { ruleId: 'rule-3', averageTime: 35 }
      ],
      slowestRules: [
        { ruleId: 'rule-4', averageTime: 150 },
        { ruleId: 'rule-5', averageTime: 120 },
        { ruleId: 'rule-6', averageTime: 100 }
      ],
      mostUsedRules: [
        { ruleId: 'rule-1', usageCount: 1000 },
        { ruleId: 'rule-2', usageCount: 800 },
        { ruleId: 'rule-3', usageCount: 600 }
      ]
    }
  }

  private async getActiveAlerts(): Promise<PerformanceAlert[]> {
    // Return recent alerts from the performance monitor
    // This would typically be stored in a database
    return []
  }

  private async generateRecommendations(metrics: any): Promise<string[]> {
    const recommendations: string[] = []
    
    if (metrics.averageEvaluationTime > 100) {
      recommendations.push('Consider optimizing slow-performing rules')
    }
    
    if (metrics.errorRate > 0.05) {
      recommendations.push('Review rule configurations to reduce error rate')
    }
    
    if (metrics.cacheHitRate < 0.7) {
      recommendations.push('Optimize caching strategy to improve hit rate')
    }
    
    return recommendations
  }

  private async fetchPerformanceData(
    timeRange: { start: Date; end: Date },
    groupBy: string
  ): Promise<Array<{
    timestamp: Date
    evaluationTime: number
    errorRate: number
    cacheHitRate: number
    throughput: number
  }>> {
    // This would typically fetch from a time-series database
    // For now, return mock data
    const data: any[] = []
    const interval = groupBy === 'hour' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000
    
    for (let time = timeRange.start.getTime(); time <= timeRange.end.getTime(); time += interval) {
      data.push({
        timestamp: new Date(time),
        evaluationTime: Math.random() * 100 + 50,
        errorRate: Math.random() * 0.05,
        cacheHitRate: Math.random() * 0.3 + 0.7,
        throughput: Math.random() * 1000 + 500
      })
    }
    
    return data
  }

  private calculateSummaryMetrics(data: any[]): any {
    if (data.length === 0) {
      return {
        totalEvaluations: 0,
        averageEvaluationTime: 0,
        errorRate: 0,
        cacheHitRate: 0,
        peakThroughput: 0
      }
    }
    
    const totalEvaluations = data.reduce((sum, d) => sum + d.throughput, 0)
    const averageEvaluationTime = data.reduce((sum, d) => sum + d.evaluationTime, 0) / data.length
    const errorRate = data.reduce((sum, d) => sum + d.errorRate, 0) / data.length
    const cacheHitRate = data.reduce((sum, d) => sum + d.cacheHitRate, 0) / data.length
    const peakThroughput = Math.max(...data.map(d => d.throughput))
    
    return {
      totalEvaluations,
      averageEvaluationTime,
      errorRate,
      cacheHitRate,
      peakThroughput
    }
  }

  private generateDetailedMetrics(data: any[], config: ReportConfig): any[] {
    return data.map(row => ({
      timestamp: row.timestamp,
      metrics: config.metrics.reduce((acc, metric) => {
        switch (metric) {
          case 'evaluation_time':
            acc.evaluationTime = row.evaluationTime
            break
          case 'error_rate':
            acc.errorRate = row.errorRate
            break
          case 'cache_hit_rate':
            acc.cacheHitRate = row.cacheHitRate
            break
          case 'throughput':
            acc.throughput = row.throughput
            break
        }
        return acc
      }, {} as Record<string, number>)
    }))
  }

  private generateInsights(data: any[], summary: any): string[] {
    const insights: string[] = []
    
    if (summary.averageEvaluationTime > 100) {
      insights.push('Evaluation times are above optimal range')
    }
    
    if (summary.errorRate > 0.05) {
      insights.push('Error rate is higher than recommended threshold')
    }
    
    if (summary.cacheHitRate < 0.7) {
      insights.push('Cache performance could be improved')
    }
    
    // Trend analysis
    if (data.length > 1) {
      const firstHalf = data.slice(0, Math.floor(data.length / 2))
      const secondHalf = data.slice(Math.floor(data.length / 2))
      
      const firstAvg = firstHalf.reduce((sum, d) => sum + d.evaluationTime, 0) / firstHalf.length
      const secondAvg = secondHalf.reduce((sum, d) => sum + d.evaluationTime, 0) / secondHalf.length
      
      if (secondAvg > firstAvg * 1.1) {
        insights.push('Performance is degrading over time')
      } else if (secondAvg < firstAvg * 0.9) {
        insights.push('Performance is improving over time')
      }
    }
    
    return insights
  }
}

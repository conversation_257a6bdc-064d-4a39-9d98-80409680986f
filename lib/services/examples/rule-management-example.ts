/**
 * Rule Management Service Integration Example
 * 
 * This example demonstrates how to integrate and use the Rule Management Service
 * in a Next.js application for managing conditional link rules.
 */

import { RuleServiceFactory, RuleManagementService } from '@/lib/services'
import type { EnhancedLinkCondition } from '@/lib/rule-engine'

/**
 * Example: Setting up the Rule Management Service
 */
export async function setupRuleManagementService() {
  // Option 1: Create a fully managed service with monitoring
  const managedService = RuleServiceFactory.createManagedRuleService({
    enableMonitoring: true,
    ruleManagementOptions: {
      autoResolveConflicts: true,
      enablePerformanceMonitoring: true,
      useTransactions: true,
      validationLevel: 'moderate',
      enableOptimization: true,
      operationTimeout: 30000
    },
    monitoringConfig: {
      healthCheckInterval: 30000,
      alertThresholds: {
        responseTime: 1000,
        errorRate: 0.05,
        memoryUsage: 0.8
      },
      autoRecovery: {
        enabled: true,
        maxAttempts: 3,
        backoffMultiplier: 2
      }
    }
  })

  // Subscribe to service alerts
  managedService.monitor?.onAlert((alert) => {
    console.log(`🚨 Service Alert: ${alert.title} (${alert.severity})`)
    
    if (alert.severity === 'critical') {
      // Handle critical alerts - could send notifications, trigger recovery, etc.
      console.error('Critical alert details:', alert.description)
    }
  })

  return managedService
}

/**
 * Example: Creating conditional rules with comprehensive validation
 */
export async function createConditionalRules() {
  const ruleService = RuleManagementService.getInstance()
  
  // Example 1: Create a referrer-based rule
  const referrerRuleData: Partial<EnhancedLinkCondition> = {
    type: 'referrer',
    priority: 100,
    isActive: true,
    rules: {
      type: 'referrer',
      domains: ['instagram.com', 'facebook.com'],
      matchType: 'contains',
      caseSensitive: false
    },
    action: {
      type: 'show',
      alternateTitle: 'From Social Media',
      alternateIcon: '📱'
    }
  }

  const referrerResult = await ruleService.createRule('link-123', referrerRuleData, {
    autoResolveConflicts: true,
    enableOptimization: true,
    validationLevel: 'strict'
  })

  if (referrerResult.success) {
    console.log('✅ Referrer rule created successfully')
    console.log('Rule ID:', referrerResult.data?.id)
    console.log('Performance metrics:', referrerResult.performanceMetrics)
    console.log('Conflicts resolved:', referrerResult.metadata.conflictsResolved)
  } else {
    console.error('❌ Failed to create referrer rule:', referrerResult.errors)
  }

  // Example 2: Create a location-based rule
  const locationRuleData: Partial<EnhancedLinkCondition> = {
    type: 'location',
    priority: 90,
    isActive: true,
    rules: {
      type: 'location',
      countries: ['US', 'CA', 'GB'],
      excludeCountries: ['CN']
    },
    action: {
      type: 'show',
      alternateTitle: 'For English Speakers'
    }
  }

  const locationResult = await ruleService.createRule('link-123', locationRuleData)
  
  if (locationResult.success) {
    console.log('✅ Location rule created successfully')
  }

  return { referrerResult, locationResult }
}

/**
 * Example: Bulk rule operations with transaction support
 */
export async function performBulkRuleOperations() {
  const ruleService = RuleManagementService.getInstance()

  // Create multiple rules in a single transaction
  const bulkOperations = [
    {
      operation: 'create' as const,
      rules: [
        {
          linkId: 'link-456',
          type: 'device',
          priority: 100,
          isActive: true,
          rules: {
            type: 'device',
            deviceTypes: ['mobile'],
            platforms: ['ios', 'android']
          },
          action: { type: 'show', alternateTitle: 'Mobile App' }
        },
        {
          linkId: 'link-456',
          type: 'time',
          priority: 80,
          isActive: true,
          rules: {
            type: 'time',
            daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
            startTime: '09:00',
            endTime: '17:00',
            timezone: 'America/New_York'
          },
          action: { type: 'show', alternateTitle: 'Business Hours' }
        }
      ]
    }
  ]

  const bulkResult = await ruleService.performBulkOperation(bulkOperations, {
    useTransactions: true,
    autoResolveConflicts: true,
    enableOptimization: true
  })

  if (bulkResult.success) {
    console.log('✅ Bulk operations completed successfully')
    console.log('Created rules:', bulkResult.data?.length)
    console.log('Affected rules:', bulkResult.metadata.affectedRules)
    console.log('Total operation time:', bulkResult.performanceMetrics.operationTime, 'ms')
  } else {
    console.error('❌ Bulk operations failed:', bulkResult.errors)
  }

  return bulkResult
}

/**
 * Example: Rule validation and optimization
 */
export async function validateAndOptimizeRules() {
  const ruleService = RuleManagementService.getInstance()
  const linkId = 'link-789'

  // Comprehensive rule validation
  const ruleData: Partial<EnhancedLinkCondition> = {
    type: 'referrer',
    priority: 100,
    rules: {
      type: 'referrer',
      domains: ['example.com', 'test.com', 'demo.com'],
      matchType: 'exact',
      caseSensitive: false
    },
    action: { type: 'show' }
  }

  const validation = await ruleService.validateRuleComprehensive(ruleData, {
    linkId,
    validationLevel: 'strict'
  })

  console.log('🔍 Rule Validation Results:')
  console.log('Valid:', validation.isValid)
  console.log('Errors:', validation.errors)
  console.log('Warnings:', validation.warnings)
  console.log('Semantic issues:', validation.semanticIssues)
  console.log('Performance warnings:', validation.performanceWarnings)
  console.log('Security concerns:', validation.securityConcerns)
  console.log('Suggestions:', validation.suggestions)

  // Analyze rules for optimization
  const analysis = await ruleService.analyzeRulesForOptimization(linkId)
  
  console.log('📊 Optimization Analysis:')
  console.log('Suggestions:', analysis.suggestions)
  console.log('Potential improvements:', analysis.potentialImprovements)
  console.log('Estimated performance gain:', analysis.estimatedPerformanceGain, '%')

  // Auto-optimize rules if beneficial
  if (analysis.estimatedPerformanceGain > 10) {
    const optimization = await ruleService.optimizeRulesForLink(linkId)
    
    if (optimization.success) {
      console.log('⚡ Rules optimized successfully')
      console.log('Improvements applied:', optimization.data?.improvementsApplied)
      console.log('Performance gain:', optimization.data?.performanceGain, '%')
    }
  }

  return { validation, analysis }
}

/**
 * Example: Rule lifecycle management
 */
export async function manageRuleLifecycle() {
  const ruleService = RuleManagementService.getInstance()
  const ruleId = 'rule-example-123'

  // Check current lifecycle state
  const currentState = await ruleService.getRuleLifecycleState(ruleId)
  console.log('📋 Current rule state:', currentState)

  // Transition rule through lifecycle states
  const transitions = [
    { from: 'draft', to: 'active' as const },
    { from: 'active', to: 'inactive' as const },
    { from: 'inactive', to: 'active' as const },
    { from: 'active', to: 'archived' as const }
  ]

  for (const transition of transitions) {
    if (currentState === transition.from) {
      const result = await ruleService.transitionRuleState(ruleId, transition.to)
      
      if (result.success) {
        console.log(`✅ Rule transitioned from ${transition.from} to ${transition.to}`)
      } else {
        console.error(`❌ Failed to transition rule:`, result.errors)
      }
      break
    }
  }
}

/**
 * Example: Performance monitoring and health checks
 */
export async function monitorServicePerformance() {
  const managedService = RuleServiceFactory.createManagedRuleService()
  
  // Get comprehensive service status
  const status = await managedService.getStatus()
  
  console.log('🏥 Service Health Status:')
  console.log('Service status:', status.service.status)
  console.log('Uptime:', Math.round(status.service.uptime / 1000), 'seconds')
  console.log('Operations performed:', status.service.operationsPerformed)
  console.log('Average response time:', status.service.averageResponseTime, 'ms')
  console.log('Error rate:', (status.service.errorRate * 100).toFixed(2), '%')

  console.log('📊 Real-time Performance:')
  console.log('Current status:', status.realTime.status)
  console.log('Evaluation time:', status.realTime.currentMetrics.evaluationTime, 'ms')
  console.log('Cache hit rate:', (status.realTime.currentMetrics.cacheHitRate * 100).toFixed(1), '%')
  console.log('Throughput:', status.realTime.currentMetrics.throughput, 'ops/sec')

  if (status.monitoring) {
    console.log('🔍 Monitoring Status:')
    console.log('Active alerts:', status.monitoring.activeAlerts.length)
    console.log('Last health check:', status.monitoring.lastHealthCheck)
  }

  // Get dashboard metrics
  const dashboardMetrics = await managedService.dashboard.getDashboardMetrics()
  
  console.log('📈 Dashboard Overview:')
  console.log('Total rules:', dashboardMetrics.overview.totalRules)
  console.log('Active rules:', dashboardMetrics.overview.activeRules)
  console.log('Success rate:', (dashboardMetrics.overview.successRate * 100).toFixed(1), '%')
  console.log('Recommendations:', dashboardMetrics.recommendations)

  return { status, dashboardMetrics }
}

/**
 * Example: Error handling and recovery
 */
export async function handleErrorsAndRecovery() {
  const ruleService = RuleManagementService.getInstance()

  try {
    // Attempt an operation that might fail
    const result = await ruleService.createRule('invalid-link-id', {
      type: 'invalid-type' as any,
      priority: -1, // Invalid priority
      rules: {} // Invalid rules
    })

    if (!result.success) {
      console.log('🚨 Operation failed as expected:')
      console.log('Errors:', result.errors)
      console.log('Warnings:', result.warnings)
      console.log('Performance impact:', result.performanceMetrics.operationTime, 'ms')
      
      // Handle specific error types
      if (result.errors.some(e => e.includes('validation'))) {
        console.log('💡 Validation error detected - check rule configuration')
      }
      
      if (result.errors.some(e => e.includes('conflict'))) {
        console.log('💡 Conflict detected - consider using auto-resolution')
      }
    }
  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }

  // Demonstrate manual recovery
  const monitor = RuleServiceFactory.createServiceMonitor()
  
  console.log('🔧 Triggering manual recovery actions:')
  
  const recoveryActions = ['clear_cache', 'restart_service'] as const
  
  for (const action of recoveryActions) {
    const recovery = await monitor.triggerRecovery(action)
    console.log(`${recovery.success ? '✅' : '❌'} Recovery action '${action}': ${recovery.description}`)
  }
}

/**
 * Complete example usage
 */
export async function completeExample() {
  console.log('🚀 Starting Rule Management Service Example')
  
  try {
    // 1. Setup the service
    const managedService = await setupRuleManagementService()
    console.log('✅ Service setup complete')

    // 2. Create some rules
    await createConditionalRules()
    console.log('✅ Rules created')

    // 3. Perform bulk operations
    await performBulkRuleOperations()
    console.log('✅ Bulk operations complete')

    // 4. Validate and optimize
    await validateAndOptimizeRules()
    console.log('✅ Validation and optimization complete')

    // 5. Monitor performance
    await monitorServicePerformance()
    console.log('✅ Performance monitoring complete')

    // 6. Demonstrate error handling
    await handleErrorsAndRecovery()
    console.log('✅ Error handling demonstration complete')

    // 7. Cleanup
    managedService.shutdown()
    console.log('✅ Service shutdown complete')

  } catch (error) {
    console.error('💥 Example failed:', error)
  }
  
  console.log('🏁 Rule Management Service Example Complete')
}

// Export for use in other files
export default {
  setupRuleManagementService,
  createConditionalRules,
  performBulkRuleOperations,
  validateAndOptimizeRules,
  manageRuleLifecycle,
  monitorServicePerformance,
  handleErrorsAndRecovery,
  completeExample
}

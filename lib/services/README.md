# Rule Management Services

This directory contains the comprehensive Rule Management Service implementation for the LinksInBio application's conditional link system. The services provide high-level rule management operations, transaction support, validation, conflict resolution, and performance monitoring.

## Architecture Overview

The Rule Management Services are built on top of the existing rule engine and provide a service layer that handles:

- **High-level rule operations** with transaction support
- **Comprehensive validation** and conflict resolution
- **Performance monitoring** and health checks
- **Automatic optimization** and recovery
- **Service-level analytics** and reporting

## Core Services

### 1. RuleManagementService (`rule-management.ts`)

The main service that provides high-level rule management operations.

**Key Features:**
- Transaction-based rule operations (create, update, delete)
- Automatic conflict detection and resolution
- Rule lifecycle management (draft → active → archived)
- Bulk operations with rollback support
- Performance optimization suggestions
- Comprehensive validation with semantic analysis

**Usage:**
```typescript
import { RuleManagementService } from '@/lib/services'

const ruleService = RuleManagementService.getInstance({
  autoResolveConflicts: true,
  enablePerformanceMonitoring: true,
  useTransactions: true,
  validationLevel: 'moderate'
})

// Create a new rule with validation and conflict checking
const result = await ruleService.createRule(linkId, ruleData, {
  autoResolveConflicts: true,
  enableOptimization: true
})

if (result.success) {
  console.log('Rule created:', result.data)
  console.log('Performance:', result.performanceMetrics)
} else {
  console.error('Errors:', result.errors)
}
```

### 2. RulePerformanceDashboard (`rule-performance-dashboard.ts`)

Provides comprehensive performance monitoring and analytics.

**Key Features:**
- Real-time performance metrics
- Historical trend analysis
- Performance report generation
- Alert management
- Data export capabilities

**Usage:**
```typescript
import { RulePerformanceDashboard } from '@/lib/services'

const dashboard = RulePerformanceDashboard.getInstance()

// Get comprehensive dashboard metrics
const metrics = await dashboard.getDashboardMetrics()
console.log('Overview:', metrics.overview)
console.log('Trends:', metrics.trends)
console.log('Recommendations:', metrics.recommendations)

// Generate performance report
const report = await dashboard.generatePerformanceReport({
  timeRange: { start: new Date('2024-01-01'), end: new Date() },
  includeDetails: true,
  groupBy: 'day',
  metrics: ['evaluation_time', 'error_rate', 'cache_hit_rate']
})
```

### 3. RuleServiceMonitor (`rule-service-monitor.ts`)

Provides continuous monitoring, health checking, and automated recovery.

**Key Features:**
- Continuous health monitoring
- Automated recovery actions
- Alert generation and notification
- Service status tracking
- Performance threshold monitoring

**Usage:**
```typescript
import { RuleServiceMonitor } from '@/lib/services'

const monitor = RuleServiceMonitor.getInstance({
  healthCheckInterval: 30000,
  alertThresholds: {
    responseTime: 1000,
    errorRate: 0.05,
    memoryUsage: 0.8
  },
  autoRecovery: {
    enabled: true,
    maxAttempts: 3
  }
})

// Start monitoring
monitor.startMonitoring()

// Subscribe to alerts
monitor.onAlert((alert) => {
  console.log('Service Alert:', alert.title, alert.severity)
  if (alert.severity === 'critical') {
    // Handle critical alerts
  }
})

// Get current service status
const status = await monitor.getServiceStatus()
console.log('Service Status:', status.status)
console.log('Active Alerts:', status.activeAlerts.length)
```

## Service Factory

The `RuleServiceFactory` provides convenient methods to create and configure services:

```typescript
import { RuleServiceFactory } from '@/lib/services'

// Create fully managed service with monitoring
const managedService = RuleServiceFactory.createManagedRuleService({
  enableMonitoring: true,
  ruleManagementOptions: {
    autoResolveConflicts: true,
    validationLevel: 'strict'
  },
  monitoringConfig: {
    healthCheckInterval: 15000,
    autoRecovery: { enabled: true }
  }
})

// Get comprehensive status
const status = await managedService.getStatus()
console.log('Service Health:', status.service.status)
console.log('Real-time Status:', status.realTime.status)

// Shutdown gracefully
managedService.shutdown()
```

## Configuration

### Environment-based Configuration

```typescript
import { RuleServiceUtils } from '@/lib/services'

// Get recommended config for environment
const config = RuleServiceUtils.getRecommendedConfig('production')

// Create config from environment variables
const envConfig = RuleServiceUtils.createConfigFromEnv()

// Validate configuration
const validation = RuleServiceUtils.validateConfig(config)
if (!validation.valid) {
  console.error('Config errors:', validation.errors)
}
```

### Environment Variables

```bash
# Rule Management Configuration
RULE_AUTO_RESOLVE_CONFLICTS=true
RULE_PERFORMANCE_MONITORING=true
RULE_USE_TRANSACTIONS=true
RULE_VALIDATION_LEVEL=moderate
RULE_ENABLE_OPTIMIZATION=true
RULE_OPERATION_TIMEOUT=30000

# Monitoring Configuration
RULE_HEALTH_CHECK_INTERVAL=30000
RULE_ALERT_RESPONSE_TIME=1000
RULE_ALERT_ERROR_RATE=0.05
RULE_ALERT_MEMORY_USAGE=0.8
RULE_AUTO_RECOVERY=true
RULE_RECOVERY_MAX_ATTEMPTS=3
RULE_RECOVERY_BACKOFF=2
```

## Rule Lifecycle Management

The service supports comprehensive rule lifecycle management:

```typescript
// Check rule lifecycle state
const state = await ruleService.getRuleLifecycleState(ruleId)
console.log('Current state:', state) // 'draft' | 'active' | 'inactive' | 'conflicted' | 'archived'

// Transition rule state
const result = await ruleService.transitionRuleState(ruleId, 'active')
if (result.success) {
  console.log('Rule activated successfully')
}
```

## Bulk Operations

Efficient bulk operations with transaction support:

```typescript
const bulkOperations = [
  {
    operation: 'create',
    rules: [ruleData1, ruleData2, ruleData3]
  },
  {
    operation: 'update',
    rules: [updatedRule1, updatedRule2]
  },
  {
    operation: 'activate',
    rules: [{ id: 'rule-1' }, { id: 'rule-2' }]
  }
]

const result = await ruleService.performBulkOperation(bulkOperations, {
  useTransactions: true,
  autoResolveConflicts: true
})

console.log('Bulk operation results:', result.data)
console.log('Affected rules:', result.metadata.affectedRules)
```

## Advanced Validation

Comprehensive validation with semantic analysis:

```typescript
const validation = await ruleService.validateRuleComprehensive(ruleData, {
  linkId: 'link-123',
  validationLevel: 'strict'
})

console.log('Validation result:', validation.isValid)
console.log('Semantic issues:', validation.semanticIssues)
console.log('Performance warnings:', validation.performanceWarnings)
console.log('Security concerns:', validation.securityConcerns)
console.log('Suggestions:', validation.suggestions)
```

## Optimization and Analytics

Rule optimization and performance analysis:

```typescript
// Analyze rules for optimization opportunities
const analysis = await ruleService.analyzeRulesForOptimization(linkId)
console.log('Optimization suggestions:', analysis.suggestions)
console.log('Potential improvements:', analysis.potentialImprovements)
console.log('Estimated performance gain:', analysis.estimatedPerformanceGain)

// Auto-optimize rules
const optimization = await ruleService.optimizeRulesForLink(linkId)
if (optimization.success) {
  console.log('Optimized rules:', optimization.data.optimizedRules.length)
  console.log('Improvements applied:', optimization.data.improvementsApplied)
  console.log('Performance gain:', optimization.data.performanceGain)
}
```

## Error Handling

The services provide comprehensive error handling with detailed error information:

```typescript
const result = await ruleService.createRule(linkId, ruleData)

if (!result.success) {
  console.error('Operation failed:')
  console.error('Errors:', result.errors)
  console.error('Warnings:', result.warnings)
  console.error('Performance impact:', result.performanceMetrics)
}
```

## Integration with Existing Systems

The services integrate seamlessly with the existing rule engine and repositories:

- **Rule Engine Integration**: Uses the enhanced rule evaluator for validation and conflict detection
- **Repository Integration**: Leverages existing LinkRepository and LinkConditionRepository
- **Performance Monitoring**: Integrates with RulePerformanceMonitor for metrics collection
- **Error Handling**: Uses the existing error handling system with enhanced error types

## Testing

The services are designed to be easily testable with comprehensive mocking support:

```typescript
// Mock the service for testing
const mockRuleService = {
  createRule: jest.fn().mockResolvedValue({ success: true, data: mockRule }),
  updateRule: jest.fn(),
  deleteRule: jest.fn()
}

// Test service operations
const result = await mockRuleService.createRule('link-1', ruleData)
expect(result.success).toBe(true)
```

## Performance Considerations

- **Transaction Support**: All operations can be wrapped in database transactions
- **Caching**: Rule evaluation results are cached for improved performance
- **Batch Operations**: Bulk operations are optimized for performance
- **Monitoring**: Continuous performance monitoring with alerting
- **Auto-optimization**: Automatic rule optimization based on performance analysis

## Security

- **Input Validation**: Comprehensive validation of all rule data
- **SQL Injection Protection**: Uses parameterized queries and ORM
- **Access Control**: Service-level access control (to be implemented)
- **Audit Logging**: All operations are logged for audit purposes
- **Rate Limiting**: Built-in rate limiting for rule operations

## Future Enhancements

- **Rule Templates**: Pre-built rule templates for common use cases
- **A/B Testing**: Built-in A/B testing support for rules
- **Machine Learning**: ML-based rule optimization suggestions
- **Advanced Analytics**: More sophisticated performance analytics
- **Multi-tenant Support**: Support for multiple tenants/organizations

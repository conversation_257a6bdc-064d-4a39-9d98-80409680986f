// Mock Prisma client first
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    linkCondition: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      groupBy: jest.fn(),
      count: jest.fn(),
      aggregate: jest.fn(),
    },
    link: {
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
    $queryRaw: jest.fn(),
  })),
  Prisma: {
    sql: jest.fn(),
    empty: jest.fn(),
  }
}))

// Mock the database completely
jest.mock('../../db', () => {
  const mockDb = {
    linkCondition: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      groupBy: jest.fn(),
      count: jest.fn(),
      aggregate: jest.fn(),
    },
    link: {
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
    $queryRaw: jest.fn(),
  }

  return {
    db: mockDb,
    handleDatabaseError: jest.fn((error) => { throw error }),
    NotFoundError: class NotFoundError extends Error {
      constructor(resource: string, id: string) {
        super(`${resource} with id ${id} not found`)
        this.name = 'NotFoundError'
      }
    },
    ConflictError: class ConflictError extends Error {
      constructor(message: string) {
        super(message)
        this.name = 'ConflictError'
      }
    },
    ValidationError: class ValidationError extends Error {
      constructor(message: string) {
        super(message)
        this.name = 'ValidationError'
      }
    },
    withTransaction: jest.fn((callback) => callback(mockDb)),
  }
})

// Mock the rule engine
jest.mock('../../rule-engine/rule-validator', () => ({
  RuleValidator: {
    validateRule: jest.fn(() => ({ isValid: true, errors: [], warnings: [] }))
  }
}))

jest.mock('../../rule-engine/conflict-resolver', () => ({
  RuleConflictResolver: {
    detectConflicts: jest.fn(() => ({ hasConflicts: false, conflicts: [], warnings: [], suggestions: [] }))
  }
}))

import { LinkConditionRepository } from '../link-condition'
import type { CreateLinkConditionData, UpdateLinkConditionData } from '../index'

// Import after mocking
const { db } = require('../../db')
const mockDb = db as any

describe('LinkConditionRepository', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Clear console spies
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'warn').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('create', () => {
    it('should create a new link condition successfully', async () => {
      // Arrange
      const conditionData: CreateLinkConditionData = {
        linkId: 'clh7x8y9z0000abcdefghijk', // Valid CUID format
        type: 'referrer',
        priority: 100,
        isActive: true,
        rules: {
          domains: ['twitter.com', 'x.com'],
          matchType: 'exact',
          caseSensitive: false
        },
        action: {
          type: 'show',
          alternateTitle: 'From Social Media'
        }
      }
      const mockLink = {
        id: 'clh7x8y9z0000abcdefghijk',
        conditions: [],
        defaultBehavior: 'show'
      }
      const mockCreatedCondition = {
        id: 'clh7x8y9z0001abcdefghijk', // Valid CUID format
        ...conditionData,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockDb.link.findUnique.mockResolvedValue(mockLink)
      mockDb.linkCondition.create.mockResolvedValue(mockCreatedCondition)

      // Act
      const result = await LinkConditionRepository.create(conditionData)

      // Assert
      expect(result).toEqual(mockCreatedCondition)
      expect(mockDb.link.findUnique).toHaveBeenCalledWith({
        where: { id: 'clh7x8y9z0000abcdefghijk' },
        include: { conditions: true }
      })
      expect(mockDb.linkCondition.create).toHaveBeenCalledWith({
        data: {
          ...conditionData,
          rules: conditionData.rules,
          action: conditionData.action
        }
      })
    })

    it('should throw NotFoundError when link does not exist', async () => {
      // Arrange
      const conditionData: CreateLinkConditionData = {
        linkId: 'clh7x8y9z0002abcdefghijk', // Valid CUID format
        type: 'referrer',
        priority: 100,
        isActive: true,
        rules: {
          domains: ['twitter.com'],
          matchType: 'exact',
          caseSensitive: false
        },
        action: {
          type: 'show'
        }
      }

      mockDb.link.findUnique.mockResolvedValue(null)

      // Act & Assert
      await expect(LinkConditionRepository.create(conditionData)).rejects.toThrow('Link with id clh7x8y9z0002abcdefghijk not found')
    })
  })

  describe('findById', () => {
    it('should find condition by ID', async () => {
      // Arrange
      const conditionId = 'clh7x8y9z0003abcdefghijk'
      const mockCondition = {
        id: conditionId,
        linkId: 'clh7x8y9z0000abcdefghijk',
        type: 'referrer',
        priority: 100,
        isActive: true,
        rules: { domains: ['twitter.com'], matchType: 'exact' },
        action: { type: 'show' },
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockDb.linkCondition.findUnique.mockResolvedValue(mockCondition)

      // Act
      const result = await LinkConditionRepository.findById(conditionId)

      // Assert
      expect(result).toEqual(mockCondition)
      expect(mockDb.linkCondition.findUnique).toHaveBeenCalledWith({
        where: { id: conditionId }
      })
    })

    it('should return null when condition not found', async () => {
      // Arrange
      const conditionId = 'clh7x8y9z0004abcdefghijk'
      mockDb.linkCondition.findUnique.mockResolvedValue(null)

      // Act
      const result = await LinkConditionRepository.findById(conditionId)

      // Assert
      expect(result).toBeNull()
    })
  })

  describe('findByLinkId', () => {
    it('should find conditions by link ID ordered by priority', async () => {
      // Arrange
      const linkId = 'clh7x8y9z0000abcdefghijk'
      const mockConditions = [
        {
          id: 'clh7x8y9z0005abcdefghijk',
          linkId,
          priority: 100,
          createdAt: new Date('2023-01-01'),
          type: 'referrer'
        },
        {
          id: 'clh7x8y9z0006abcdefghijk',
          linkId,
          priority: 80,
          createdAt: new Date('2023-01-02'),
          type: 'location'
        }
      ]

      mockDb.linkCondition.findMany.mockResolvedValue(mockConditions)

      // Act
      const result = await LinkConditionRepository.findByLinkId(linkId)

      // Assert
      expect(result).toEqual(mockConditions)
      expect(mockDb.linkCondition.findMany).toHaveBeenCalledWith({
        where: { linkId },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      })
    })
  })

  describe('findActiveByLinkId', () => {
    it('should find only active conditions by link ID', async () => {
      // Arrange
      const linkId = 'clh7x8y9z0000abcdefghijk'
      const mockActiveConditions = [
        {
          id: 'clh7x8y9z0007abcdefghijk',
          linkId,
          priority: 100,
          isActive: true,
          type: 'referrer'
        }
      ]

      mockDb.linkCondition.findMany.mockResolvedValue(mockActiveConditions)

      // Act
      const result = await LinkConditionRepository.findActiveByLinkId(linkId)

      // Assert
      expect(result).toEqual(mockActiveConditions)
      expect(mockDb.linkCondition.findMany).toHaveBeenCalledWith({
        where: {
          linkId,
          isActive: true
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      })
    })
  })

  describe('update', () => {
    it('should update condition successfully', async () => {
      // Arrange
      const conditionId = 'clh7x8y9z0008abcdefghijk'
      const updateData: UpdateLinkConditionData = {
        priority: 150,
        isActive: false
      }
      const existingCondition = {
        id: conditionId,
        linkId: 'clh7x8y9z0000abcdefghijk',
        type: 'referrer',
        priority: 100,
        isActive: true,
        rules: { domains: ['twitter.com'], matchType: 'exact' },
        action: { type: 'show' }
      }
      const updatedCondition = {
        ...existingCondition,
        ...updateData,
        updatedAt: new Date()
      }

      mockDb.linkCondition.findUnique.mockResolvedValue(existingCondition)
      mockDb.linkCondition.update.mockResolvedValue(updatedCondition)

      // Act
      const result = await LinkConditionRepository.update(conditionId, updateData)

      // Assert
      expect(result).toEqual(updatedCondition)
      expect(mockDb.linkCondition.update).toHaveBeenCalledWith({
        where: { id: conditionId },
        data: updateData
      })
    })
  })

  describe('delete', () => {
    it('should delete condition successfully', async () => {
      // Arrange
      const conditionId = 'clh7x8y9z0009abcdefghijk'
      const existingCondition = {
        id: conditionId,
        linkId: 'clh7x8y9z0000abcdefghijk',
        type: 'referrer'
      }

      mockDb.linkCondition.findUnique.mockResolvedValue(existingCondition)
      mockDb.linkCondition.delete.mockResolvedValue(existingCondition)

      // Act
      const result = await LinkConditionRepository.delete(conditionId)

      // Assert
      expect(result).toEqual(existingCondition)
      expect(mockDb.linkCondition.delete).toHaveBeenCalledWith({
        where: { id: conditionId }
      })
    })
  })
})


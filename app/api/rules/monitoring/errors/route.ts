import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { RuleErrorHandler } from '@/lib/rule-engine'
import { handleApiError, withError<PERSON>ontext, errorLogger } from '@/lib/utils/error-logger'
import { z } from 'zod'

// Request validation schemas
const errorReportSchema = z.object({
  linkId: z.string().min(1, 'Link ID is required'),
  conditionId: z.string().optional(),
  errorType: z.enum([
    'validation_error',
    'evaluation_error',
    'configuration_error',
    'network_error',
    'timeout_error',
    'system_error',
    'unknown_error'
  ]),
  errorMessage: z.string().min(1, 'Error message is required'),
  errorCode: z.string().optional(),
  stackTrace: z.string().optional(),
  visitorContext: z.record(z.any()).optional(),
  ruleConfiguration: z.record(z.any()).optional(),
  severity: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
  metadata: z.record(z.any()).optional()
})

const errorQuerySchema = z.object({
  linkId: z.string().optional(),
  conditionId: z.string().optional(),
  errorType: z.enum([
    'validation_error',
    'evaluation_error',
    'configuration_error',
    'network_error',
    'timeout_error',
    'system_error',
    'unknown_error'
  ]).optional(),
  severity: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  timeRange: z.enum(['1h', '6h', '24h', '7d', '30d']).default('24h'),
  limit: z.number().int().min(1).max(1000).default(100),
  offset: z.number().int().min(0).default(0),
  sortBy: z.enum(['timestamp', 'severity', 'frequency']).default('timestamp'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  includeResolved: z.boolean().default(false),
  groupBy: z.enum(['error_type', 'link_id', 'condition_id', 'severity']).optional()
})

const errorResolutionSchema = z.object({
  errorId: z.string().min(1, 'Error ID is required'),
  resolution: z.enum(['fixed', 'ignored', 'deferred', 'duplicate']),
  resolutionNotes: z.string().optional(),
  preventionMeasures: z.array(z.string()).optional()
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * POST /api/rules/monitoring/errors
 * Report a rule evaluation error
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = errorReportSchema.parse(body)

    // Initialize error handler
    const errorHandler = new RuleErrorHandler()

    // Create error report
    const errorReport = await errorHandler.reportError({
      userId: session.user.id,
      linkId: validatedData.linkId,
      conditionId: validatedData.conditionId,
      errorType: validatedData.errorType,
      errorMessage: validatedData.errorMessage,
      errorCode: validatedData.errorCode,
      stackTrace: validatedData.stackTrace,
      visitorContext: validatedData.visitorContext,
      ruleConfiguration: validatedData.ruleConfiguration,
      severity: validatedData.severity,
      metadata: validatedData.metadata,
      timestamp: new Date()
    })

    // Log the error for monitoring
    errorLogger.error(
      `Rule evaluation error: ${validatedData.errorMessage}`,
      {
        userId: session.user.id,
        linkId: validatedData.linkId,
        conditionId: validatedData.conditionId,
        errorType: validatedData.errorType,
        severity: validatedData.severity
      }
    )

    // Check if this error pattern requires immediate attention
    const similarErrors = await errorHandler.findSimilarErrors(errorReport.id, {
      timeWindow: '1h',
      threshold: 5
    })

    let alertTriggered = false
    if (similarErrors.length >= 5) {
      await errorHandler.triggerAlert({
        type: 'error_spike',
        errorPattern: errorReport.errorType,
        linkId: validatedData.linkId,
        count: similarErrors.length,
        severity: 'high'
      })
      alertTriggered = true
    }

    return NextResponse.json({
      errorReport: {
        id: errorReport.id,
        status: 'reported',
        severity: validatedData.severity,
        alertTriggered,
        similarErrorsCount: similarErrors.length
      },
      recommendations: await generateErrorRecommendations(validatedData, similarErrors),
      timestamp: new Date().toISOString()
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid error report data',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'error reporting', withErrorContext(request))
  }
}

/**
 * GET /api/rules/monitoring/errors
 * Retrieve error reports with filtering and analytics
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const rawParams = Object.fromEntries(searchParams.entries())
    
    // Convert string parameters to appropriate types
    const processedParams = {
      ...rawParams,
      limit: rawParams.limit ? parseInt(rawParams.limit) : undefined,
      offset: rawParams.offset ? parseInt(rawParams.offset) : undefined,
      includeResolved: rawParams.includeResolved === 'true'
    }

    const validatedParams = errorQuerySchema.parse(processedParams)

    // Initialize error handler
    const errorHandler = new RuleErrorHandler()

    // Get error reports
    const errorReports = await errorHandler.getErrorReports({
      userId: session.user.id,
      linkId: validatedParams.linkId,
      conditionId: validatedParams.conditionId,
      errorType: validatedParams.errorType,
      severity: validatedParams.severity,
      timeRange: validatedParams.timeRange,
      limit: validatedParams.limit,
      offset: validatedParams.offset,
      sortBy: validatedParams.sortBy,
      sortOrder: validatedParams.sortOrder,
      includeResolved: validatedParams.includeResolved,
      groupBy: validatedParams.groupBy
    })

    // Get error analytics
    const analytics = await errorHandler.getErrorAnalytics({
      userId: session.user.id,
      linkId: validatedParams.linkId,
      timeRange: validatedParams.timeRange
    })

    // Get error trends
    const trends = await errorHandler.getErrorTrends({
      userId: session.user.id,
      linkId: validatedParams.linkId,
      timeRange: validatedParams.timeRange,
      granularity: 'hour'
    })

    // Get top error patterns
    const topPatterns = await errorHandler.getTopErrorPatterns({
      userId: session.user.id,
      timeRange: validatedParams.timeRange,
      limit: 10
    })

    return NextResponse.json({
      errors: {
        reports: errorReports.data,
        total: errorReports.total,
        analytics: {
          totalErrors: analytics.totalErrors,
          errorRate: analytics.errorRate,
          severityBreakdown: analytics.severityBreakdown,
          typeBreakdown: analytics.typeBreakdown,
          resolvedCount: analytics.resolvedCount,
          unresolvedCount: analytics.unresolvedCount
        },
        trends: trends,
        topPatterns: topPatterns,
        health: {
          score: calculateErrorHealthScore(analytics),
          status: getErrorHealthStatus(analytics),
          alerts: await getActiveErrorAlerts(session.user.id, errorHandler)
        }
      },
      pagination: {
        limit: validatedParams.limit,
        offset: validatedParams.offset,
        total: errorReports.total,
        hasMore: errorReports.total > validatedParams.offset + validatedParams.limit
      },
      metadata: {
        timeRange: validatedParams.timeRange,
        filters: {
          linkId: validatedParams.linkId,
          conditionId: validatedParams.conditionId,
          errorType: validatedParams.errorType,
          severity: validatedParams.severity
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid query parameters',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'error retrieval', withErrorContext(request))
  }
}

/**
 * PUT /api/rules/monitoring/errors/resolve
 * Mark errors as resolved
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = errorResolutionSchema.parse(body)

    // Initialize error handler
    const errorHandler = new RuleErrorHandler()

    // Resolve the error
    const resolution = await errorHandler.resolveError({
      errorId: validatedData.errorId,
      userId: session.user.id,
      resolution: validatedData.resolution,
      resolutionNotes: validatedData.resolutionNotes,
      preventionMeasures: validatedData.preventionMeasures,
      resolvedAt: new Date()
    })

    // Update error patterns and learning
    await errorHandler.updateErrorPatterns(validatedData.errorId, validatedData.resolution)

    return NextResponse.json({
      resolution: {
        errorId: validatedData.errorId,
        status: validatedData.resolution,
        resolvedAt: resolution.resolvedAt,
        notes: validatedData.resolutionNotes
      },
      message: 'Error resolved successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid resolution data',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'error resolution', withErrorContext(request))
  }
}

// Helper functions
async function generateErrorRecommendations(errorData: any, similarErrors: any[]): Promise<string[]> {
  const recommendations: string[] = []

  switch (errorData.errorType) {
    case 'validation_error':
      recommendations.push('Review rule configuration for syntax errors')
      recommendations.push('Validate input data against expected schemas')
      break
    
    case 'evaluation_error':
      recommendations.push('Check visitor context data completeness')
      recommendations.push('Review rule logic for edge cases')
      break
    
    case 'timeout_error':
      recommendations.push('Consider optimizing rule complexity')
      recommendations.push('Review external service dependencies')
      break
    
    case 'configuration_error':
      recommendations.push('Verify rule configuration parameters')
      recommendations.push('Check for missing required fields')
      break
  }

  if (similarErrors.length > 3) {
    recommendations.push('Consider implementing error prevention measures')
    recommendations.push('Review rule design patterns')
  }

  if (errorData.severity === 'critical') {
    recommendations.push('Immediate attention required - consider disabling affected rules')
  }

  return recommendations
}

function calculateErrorHealthScore(analytics: any): number {
  let score = 100

  // Penalize high error rates
  if (analytics.errorRate > 0.1) score -= 40
  else if (analytics.errorRate > 0.05) score -= 25
  else if (analytics.errorRate > 0.01) score -= 10

  // Penalize unresolved errors
  const unresolvedRatio = analytics.unresolvedCount / (analytics.totalErrors || 1)
  if (unresolvedRatio > 0.5) score -= 30
  else if (unresolvedRatio > 0.2) score -= 15

  // Penalize critical errors
  const criticalRatio = (analytics.severityBreakdown?.critical || 0) / (analytics.totalErrors || 1)
  if (criticalRatio > 0.1) score -= 20
  else if (criticalRatio > 0.05) score -= 10

  return Math.max(0, score)
}

function getErrorHealthStatus(analytics: any): string {
  const score = calculateErrorHealthScore(analytics)
  
  if (score >= 90) return 'excellent'
  if (score >= 75) return 'good'
  if (score >= 60) return 'fair'
  if (score >= 40) return 'poor'
  return 'critical'
}

async function getActiveErrorAlerts(userId: string, errorHandler: any) {
  const alerts = await errorHandler.getActiveAlerts(userId)
  
  return alerts.map((alert: any) => ({
    id: alert.id,
    type: alert.type,
    severity: alert.severity,
    message: alert.message,
    triggeredAt: alert.triggeredAt,
    linkId: alert.linkId,
    errorPattern: alert.errorPattern
  }))
}

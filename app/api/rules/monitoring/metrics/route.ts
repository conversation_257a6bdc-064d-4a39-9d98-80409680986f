import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { RulePerformanceMonitor } from '@/lib/rule-engine'
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { z } from 'zod'

// Request validation schemas
const metricsQuerySchema = z.object({
  linkId: z.string().optional(),
  conditionId: z.string().optional(),
  userId: z.string().optional(),
  timeRange: z.enum(['1h', '6h', '24h', '7d', '30d', '90d']).default('24h'),
  granularity: z.enum(['minute', 'hour', 'day']).default('hour'),
  metrics: z.array(z.enum([
    'evaluation_time',
    'throughput',
    'error_rate',
    'cache_hit_rate',
    'condition_matches',
    'fallback_usage'
  ])).default(['evaluation_time', 'throughput', 'error_rate']),
  aggregation: z.enum(['avg', 'sum', 'min', 'max', 'p50', 'p95', 'p99']).default('avg'),
  includeBreakdown: z.boolean().default(false),
  includeComparison: z.boolean().default(false),
  comparisonPeriod: z.enum(['previous_period', 'same_period_last_week', 'same_period_last_month']).optional()
})

const alertConfigSchema = z.object({
  linkId: z.string().optional(),
  conditionId: z.string().optional(),
  metricType: z.enum(['evaluation_time', 'error_rate', 'throughput', 'cache_hit_rate']),
  threshold: z.number().positive(),
  operator: z.enum(['gt', 'lt', 'gte', 'lte', 'eq']).default('gt'),
  duration: z.number().int().min(60).max(3600).default(300), // seconds
  enabled: z.boolean().default(true),
  notificationChannels: z.array(z.enum(['email', 'webhook', 'dashboard'])).default(['dashboard'])
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  INSUFFICIENT_DATA: 'INSUFFICIENT_DATA',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * GET /api/rules/monitoring/metrics
 * Get performance metrics for rule evaluation
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const rawParams = Object.fromEntries(searchParams.entries())
    
    // Convert string parameters to appropriate types
    const processedParams = {
      ...rawParams,
      metrics: rawParams.metrics ? rawParams.metrics.split(',') : undefined,
      includeBreakdown: rawParams.includeBreakdown === 'true',
      includeComparison: rawParams.includeComparison === 'true'
    }

    const validatedParams = metricsQuerySchema.parse(processedParams)

    // Initialize performance monitor
    const performanceMonitor = RulePerformanceMonitor.getInstance()
    const ruleService = RuleManagementService.getInstance({
      enablePerformanceMonitoring: true
    })

    // Get metrics based on scope
    let metricsData
    if (validatedParams.conditionId) {
      metricsData = await performanceMonitor.getConditionMetrics(
        validatedParams.conditionId,
        {
          timeRange: validatedParams.timeRange,
          granularity: validatedParams.granularity,
          metrics: validatedParams.metrics,
          aggregation: validatedParams.aggregation
        }
      )
    } else if (validatedParams.linkId) {
      metricsData = await performanceMonitor.getLinkMetrics(
        validatedParams.linkId,
        {
          timeRange: validatedParams.timeRange,
          granularity: validatedParams.granularity,
          metrics: validatedParams.metrics,
          aggregation: validatedParams.aggregation
        }
      )
    } else {
      // Get user-level metrics
      metricsData = await performanceMonitor.getUserMetrics(
        session.user.id,
        {
          timeRange: validatedParams.timeRange,
          granularity: validatedParams.granularity,
          metrics: validatedParams.metrics,
          aggregation: validatedParams.aggregation
        }
      )
    }

    // Get breakdown if requested
    let breakdown = null
    if (validatedParams.includeBreakdown) {
      breakdown = await getMetricsBreakdown(
        validatedParams,
        performanceMonitor,
        session.user.id
      )
    }

    // Get comparison data if requested
    let comparison = null
    if (validatedParams.includeComparison && validatedParams.comparisonPeriod) {
      comparison = await getMetricsComparison(
        validatedParams,
        performanceMonitor,
        session.user.id
      )
    }

    // Calculate health score
    const healthScore = calculateMetricsHealthScore(metricsData)

    // Get active alerts
    const activeAlerts = await getActiveAlerts(validatedParams, performanceMonitor)

    return NextResponse.json({
      metrics: {
        data: metricsData,
        breakdown,
        comparison,
        health: {
          score: healthScore,
          status: getHealthStatus(healthScore),
          alerts: activeAlerts,
          trends: calculateTrends(metricsData)
        }
      },
      metadata: {
        timeRange: validatedParams.timeRange,
        granularity: validatedParams.granularity,
        aggregation: validatedParams.aggregation,
        dataPoints: metricsData.dataPoints?.length || 0,
        lastUpdated: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request parameters',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'metrics retrieval', withErrorContext(request))
  }
}

/**
 * POST /api/rules/monitoring/metrics/alerts
 * Configure performance alerts
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = alertConfigSchema.parse(body)

    // Initialize performance monitor
    const performanceMonitor = RulePerformanceMonitor.getInstance()

    // Create alert configuration
    const alertConfig = await performanceMonitor.createAlert({
      userId: session.user.id,
      linkId: validatedData.linkId,
      conditionId: validatedData.conditionId,
      metricType: validatedData.metricType,
      threshold: validatedData.threshold,
      operator: validatedData.operator,
      duration: validatedData.duration,
      enabled: validatedData.enabled,
      notificationChannels: validatedData.notificationChannels
    })

    return NextResponse.json({
      alert: alertConfig,
      message: 'Alert configuration created successfully',
      timestamp: new Date().toISOString()
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid alert configuration',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'alert configuration', withErrorContext(request))
  }
}

// Helper functions
async function getMetricsBreakdown(params: any, monitor: any, userId: string) {
  const breakdown = {
    byConditionType: await monitor.getMetricsByConditionType(userId, {
      timeRange: params.timeRange,
      metrics: params.metrics
    }),
    byPriority: await monitor.getMetricsByPriority(userId, {
      timeRange: params.timeRange,
      metrics: params.metrics
    }),
    byTimeOfDay: await monitor.getMetricsByTimeOfDay(userId, {
      timeRange: params.timeRange,
      metrics: params.metrics
    }),
    topPerformers: await monitor.getTopPerformingRules(userId, {
      timeRange: params.timeRange,
      limit: 10
    }),
    bottomPerformers: await monitor.getBottomPerformingRules(userId, {
      timeRange: params.timeRange,
      limit: 10
    })
  }

  return breakdown
}

async function getMetricsComparison(params: any, monitor: any, userId: string) {
  let comparisonTimeRange
  
  switch (params.comparisonPeriod) {
    case 'previous_period':
      comparisonTimeRange = getPreviousPeriod(params.timeRange)
      break
    case 'same_period_last_week':
      comparisonTimeRange = getSamePeriodLastWeek(params.timeRange)
      break
    case 'same_period_last_month':
      comparisonTimeRange = getSamePeriodLastMonth(params.timeRange)
      break
    default:
      comparisonTimeRange = getPreviousPeriod(params.timeRange)
  }

  const comparisonData = await monitor.getUserMetrics(userId, {
    timeRange: comparisonTimeRange,
    granularity: params.granularity,
    metrics: params.metrics,
    aggregation: params.aggregation
  })

  return {
    period: params.comparisonPeriod,
    data: comparisonData,
    changes: calculateMetricChanges(params.metrics, comparisonData)
  }
}

function calculateMetricsHealthScore(metricsData: any): number {
  let score = 100

  // Penalize high evaluation times
  if (metricsData.evaluation_time?.avg > 100) score -= 20
  else if (metricsData.evaluation_time?.avg > 50) score -= 10

  // Penalize high error rates
  if (metricsData.error_rate?.avg > 0.05) score -= 30
  else if (metricsData.error_rate?.avg > 0.01) score -= 15

  // Penalize low cache hit rates
  if (metricsData.cache_hit_rate?.avg < 0.5) score -= 20
  else if (metricsData.cache_hit_rate?.avg < 0.8) score -= 10

  // Penalize low throughput
  if (metricsData.throughput?.avg < 10) score -= 15
  else if (metricsData.throughput?.avg < 50) score -= 5

  return Math.max(0, score)
}

function getHealthStatus(score: number): string {
  if (score >= 90) return 'excellent'
  if (score >= 75) return 'good'
  if (score >= 60) return 'fair'
  if (score >= 40) return 'poor'
  return 'critical'
}

async function getActiveAlerts(params: any, monitor: any) {
  // Get active alerts for the specified scope
  const alerts = await monitor.getActiveAlerts({
    linkId: params.linkId,
    conditionId: params.conditionId,
    userId: params.userId
  })

  return alerts.map((alert: any) => ({
    id: alert.id,
    metricType: alert.metricType,
    threshold: alert.threshold,
    currentValue: alert.currentValue,
    severity: alert.severity,
    message: alert.message,
    timestamp: alert.timestamp instanceof Date ? alert.timestamp.toISOString() : alert.timestamp,
    triggeredAt: alert.timestamp instanceof Date ? alert.timestamp.toISOString() : alert.timestamp,
    duration: alert.duration || null
  }))
}

function calculateTrends(metricsData: any) {
  const trends: Record<string, string> = {}

  for (const [metric, data] of Object.entries(metricsData)) {
    if (data && typeof data === 'object' && 'dataPoints' in data) {
      const dataPoints = (data as any).dataPoints
      if (dataPoints && dataPoints.length >= 2) {
        const recent = dataPoints.slice(-5).reduce((sum: number, point: any) => sum + point.value, 0) / 5
        const older = dataPoints.slice(-10, -5).reduce((sum: number, point: any) => sum + point.value, 0) / 5
        
        const change = (recent - older) / older
        
        if (change > 0.1) trends[metric] = 'increasing'
        else if (change < -0.1) trends[metric] = 'decreasing'
        else trends[metric] = 'stable'
      } else {
        trends[metric] = 'insufficient_data'
      }
    }
  }

  return trends
}

function getPreviousPeriod(timeRange: string): string {
  // Simple implementation - in practice, this would calculate the exact previous period
  return timeRange
}

function getSamePeriodLastWeek(timeRange: string): string {
  // Calculate the same period one week ago
  return timeRange
}

function getSamePeriodLastMonth(timeRange: string): string {
  // Calculate the same period one month ago
  return timeRange
}

function calculateMetricChanges(metrics: string[], comparisonData: any) {
  const changes: Record<string, any> = {}

  for (const metric of metrics) {
    if (comparisonData[metric]) {
      changes[metric] = {
        absolute: comparisonData[metric].avg,
        percentage: 0, // Would calculate based on current vs comparison
        trend: 'stable' // Would determine based on comparison
      }
    }
  }

  return changes
}

/**
 * Integration tests for Rule Priority Fallback System API endpoints
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'

// Mock Next.js server components before importing
Object.defineProperty(globalThis, 'Request', {
  value: class MockRequest {
    constructor(public url: string, public init?: RequestInit) {}
    json() { return Promise.resolve({}) }
    text() { return Promise.resolve('') }
    headers = new Map()
  }
})

Object.defineProperty(globalThis, 'Response', {
  value: class MockResponse {
    constructor(public body?: any, public init?: ResponseInit) {}
    json() { return Promise.resolve(this.body) }
    text() { return Promise.resolve(this.body) }
    status = 200
    headers = new Map()
  }
})

// Mock auth
jest.mock('@/auth', () => ({
  auth: jest.fn()
}))

// Mock database
jest.mock('@/lib/db', () => ({
  db: {
    linkCondition: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    },
    link: {
      findUnique: jest.fn(),
      findMany: jest.fn()
    }
  }
}))

// Mock services
jest.mock('@/lib/services/rule-management', () => ({
  RuleManagementService: {
    getInstance: jest.fn(() => ({
      createRule: jest.fn().mockResolvedValue({
        success: true,
        data: { id: 'test-condition-id', type: 'referrer' },
        conflicts: [],
        warnings: [],
        performanceMetrics: {}
      }),
      updateRule: jest.fn().mockResolvedValue({
        success: true,
        data: { id: 'test-condition-id', type: 'referrer' }
      }),
      deleteRule: jest.fn().mockResolvedValue({ success: true }),
      validateRuleComprehensive: jest.fn().mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: [],
        semanticIssues: [],
        performanceWarnings: [],
        securityConcerns: [],
        compatibilityIssues: [],
        suggestions: []
      }),
      detectConflicts: jest.fn().mockResolvedValue({
        hasConflicts: false,
        conflicts: [],
        warnings: [],
        suggestions: []
      }),
      resolveConflicts: jest.fn().mockResolvedValue({
        success: true,
        conflictsResolved: 0,
        changes: []
      }),
      getPerformanceMetrics: jest.fn().mockResolvedValue({
        currentEvaluationTime: 10,
        averageEvaluationTime: 15,
        p95EvaluationTime: 25
      }),
      getLinkWithConditions: jest.fn().mockResolvedValue({
        id: 'test-link-id',
        conditions: []
      })
    }))
  }
}))

// Mock rule engine
jest.mock('@/lib/rule-engine', () => ({
  EnhancedRuleEvaluator: jest.fn().mockImplementation(() => ({
    evaluate: jest.fn().mockResolvedValue({
      linkId: 'test-link-id',
      shouldShow: true,
      matchedConditions: [],
      appliedAction: { type: 'show' },
      evaluationTime: 10,
      fallbackUsed: false,
      performanceMetrics: {},
      effectiveTitle: 'Test Link',
      effectiveUrl: 'https://example.com',
      metadata: {
        strategy: 'priority',
        skippedConditions: [],
        warnings: []
      }
    })
  })),
  RuleConflictResolver: {
    detectConflicts: jest.fn().mockReturnValue({
      hasConflicts: false,
      conflicts: [],
      warnings: []
    })
  },
  RulePerformanceMonitor: jest.fn().mockImplementation(() => ({
    getConditionMetrics: jest.fn().mockResolvedValue({}),
    getLinkMetrics: jest.fn().mockResolvedValue({}),
    getUserMetrics: jest.fn().mockResolvedValue({}),
    createAlert: jest.fn().mockResolvedValue({ id: 'alert-id' }),
    getActiveAlerts: jest.fn().mockResolvedValue([])
  })),
  RuleErrorHandler: jest.fn().mockImplementation(() => ({
    reportError: jest.fn().mockResolvedValue({ id: 'error-id' }),
    findSimilarErrors: jest.fn().mockResolvedValue([]),
    triggerAlert: jest.fn().mockResolvedValue({}),
    getErrorReports: jest.fn().mockResolvedValue({ data: [], total: 0 }),
    getErrorAnalytics: jest.fn().mockResolvedValue({
      totalErrors: 0,
      errorRate: 0,
      severityBreakdown: {},
      typeBreakdown: {},
      resolvedCount: 0,
      unresolvedCount: 0
    }),
    getErrorTrends: jest.fn().mockResolvedValue([]),
    getTopErrorPatterns: jest.fn().mockResolvedValue([]),
    resolveError: jest.fn().mockResolvedValue({ resolvedAt: new Date() }),
    updateErrorPatterns: jest.fn().mockResolvedValue({}),
    getActiveAlerts: jest.fn().mockResolvedValue([])
  }))
}))

// Mock visitor context
jest.mock('@/lib/utils/conditional-visitor-context', () => ({
  ConditionalVisitorContext: jest.fn().mockImplementation((data) => ({
    ...data,
    toJSON: () => data
  }))
}))

// Mock repositories
jest.mock('@/lib/repositories/link-condition', () => ({
  LinkConditionRepository: {
    findById: jest.fn().mockResolvedValue({ id: 'test-condition-id' }),
    findByLinkId: jest.fn().mockResolvedValue([]),
    findMany: jest.fn().mockResolvedValue([]),
    create: jest.fn().mockResolvedValue({ id: 'test-condition-id' }),
    update: jest.fn().mockResolvedValue({ id: 'test-condition-id' }),
    delete: jest.fn().mockResolvedValue({})
  }
}))

jest.mock('@/lib/repositories/analytics', () => ({
  AnalyticsRepository: jest.fn().mockImplementation(() => ({
    getRuleEvaluationStats: jest.fn().mockResolvedValue({}),
    getConditionMatchStats: jest.fn().mockResolvedValue({}),
    getFallbackUsageStats: jest.fn().mockResolvedValue({}),
    getCachePerformanceStats: jest.fn().mockResolvedValue({}),
    getVisitorMetrics: jest.fn().mockResolvedValue({})
  }))
}))

// Mock error logger
jest.mock('@/lib/utils/error-logger', () => ({
  handleApiError: jest.fn().mockReturnValue(new Response(JSON.stringify({ error: 'Server error' }), { status: 500 })),
  withErrorContext: jest.fn().mockReturnValue({}),
  errorLogger: {
    error: jest.fn()
  }
}))

// Helper function to create mock NextRequest
function createMockRequest(url: string, options: { method?: string; body?: any; headers?: Record<string, string> } = {}) {
  return new Request(url, {
    method: options.method || 'GET',
    headers: options.headers || {},
    body: options.body ? JSON.stringify(options.body) : undefined
  }) as any
}

describe('Rule Management API Endpoints', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>'
    }
  }

  beforeEach(() => {
    const { auth } = require('@/auth')
    auth.mockResolvedValue(mockSession)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('POST /api/rules/conditions', () => {
    it('should create a new condition successfully', async () => {
      const { POST } = await import('../conditions/route')

      const requestBody = {
        linkId: 'test-link-id',
        type: 'referrer',
        priority: 100,
        isActive: true,
        rules: {
          domains: ['facebook.com', 'instagram.com'],
          matchType: 'contains'
        },
        action: {
          type: 'show',
          alternateTitle: 'From Social Media'
        }
      }

      const request = createMockRequest('http://localhost/api/rules/conditions', {
        method: 'POST',
        body: requestBody,
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data).toHaveProperty('condition')
      expect(data).toHaveProperty('timestamp')
    })

    it('should return validation error for invalid data', async () => {
      const { POST } = await import('../conditions/route')

      const requestBody = {
        // Missing required fields
        type: 'referrer'
      }

      const request = createMockRequest('http://localhost/api/rules/conditions', {
        method: 'POST',
        body: requestBody,
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.code).toBe('VALIDATION_ERROR')
      expect(data).toHaveProperty('details')
    })

    it('should return unauthorized for unauthenticated requests', async () => {
      const { auth } = require('@/auth')
      auth.mockResolvedValue(null)

      const { POST } = await import('../conditions/route')

      const request = createMockRequest('http://localhost/api/rules/conditions', {
        method: 'POST',
        body: {},
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.code).toBe('UNAUTHORIZED')
    })
  })

  describe('GET /api/rules/conditions', () => {
    it('should retrieve conditions with default parameters', async () => {
      const { GET } = await import('../conditions/route')

      const request = createMockRequest('http://localhost/api/rules/conditions')

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('conditions')
      expect(data).toHaveProperty('pagination')
      expect(data.pagination).toHaveProperty('limit')
      expect(data.pagination).toHaveProperty('offset')
    })

    it('should handle query parameters correctly', async () => {
      const { GET } = await import('../conditions/route')

      const request = createMockRequest('http://localhost/api/rules/conditions?linkId=test-link&limit=10&sortBy=priority&sortOrder=desc')

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.pagination.limit).toBe(10)
    })
  })

  describe('POST /api/rules/conditions/validate', () => {
    it('should validate condition configuration', async () => {
      const { POST } = await import('../conditions/validate/route')

      const requestBody = {
        linkId: 'test-link-id',
        type: 'referrer',
        priority: 100,
        isActive: true,
        rules: {
          domains: ['facebook.com'],
          matchType: 'contains'
        },
        action: {
          type: 'show'
        },
        validationLevel: 'moderate',
        checkConflicts: true
      }

      const request = createMockRequest('http://localhost/api/rules/conditions/validate', {
        method: 'POST',
        body: requestBody,
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('valid')
      expect(data).toHaveProperty('validation')
      expect(data).toHaveProperty('timestamp')
    })
  })
})

describe('Rule Simulation API Endpoints', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>'
    }
  }

  beforeEach(() => {
    const { auth } = require('@/auth')
    auth.mockResolvedValue(mockSession)
  })

  describe('POST /api/rules/simulation/evaluate', () => {
    it('should simulate rule evaluation', async () => {
      const { POST } = await import('../simulation/evaluate/route')

      const requestBody = {
        linkId: 'test-link-id',
        visitorContext: {
          referrer: 'facebook.com',
          deviceType: 'mobile',
          country: 'US'
        },
        simulationOptions: {
          includePerformanceMetrics: true,
          includeDebugInfo: false
        }
      }

      const request = createMockRequest('http://localhost/api/rules/simulation/evaluate', {
        method: 'POST',
        body: requestBody,
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('simulation')
      expect(data).toHaveProperty('analysis')
      expect(data.simulation).toHaveProperty('result')
    })
  })

  describe('POST /api/rules/simulation/performance', () => {
    it('should run performance tests', async () => {
      const { POST } = await import('../simulation/performance/route')

      const requestBody = {
        linkId: 'test-link-id',
        testConfiguration: {
          duration: 30,
          concurrency: 5,
          requestsPerSecond: 10,
          testType: 'load'
        },
        visitorProfiles: [{
          name: 'default',
          weight: 1.0,
          context: {}
        }]
      }

      const request = createMockRequest('http://localhost/api/rules/simulation/performance', {
        method: 'POST',
        body: requestBody,
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('performanceTest')
      expect(data.performanceTest).toHaveProperty('results')
      expect(data.performanceTest).toHaveProperty('analysis')
    })
  })
})

describe('Rule Monitoring API Endpoints', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>'
    }
  }

  beforeEach(() => {
    const { auth } = require('@/auth')
    auth.mockResolvedValue(mockSession)
  })

  describe('GET /api/rules/monitoring/metrics', () => {
    it('should retrieve performance metrics', async () => {
      const { GET } = await import('../monitoring/metrics/route')

      const request = createMockRequest('http://localhost/api/rules/monitoring/metrics?timeRange=24h&metrics=evaluation_time,throughput')

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('metrics')
      expect(data).toHaveProperty('metadata')
      expect(data.metadata).toHaveProperty('timeRange')
    })
  })

  describe('POST /api/rules/monitoring/errors', () => {
    it('should report rule evaluation errors', async () => {
      const { POST } = await import('../monitoring/errors/route')

      const requestBody = {
        linkId: 'test-link-id',
        errorType: 'evaluation_error',
        errorMessage: 'Test error message',
        severity: 'medium',
        visitorContext: {
          referrer: 'test.com'
        }
      }

      const request = createMockRequest('http://localhost/api/rules/monitoring/errors', {
        method: 'POST',
        body: requestBody,
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data).toHaveProperty('errorReport')
      expect(data).toHaveProperty('recommendations')
    })
  })

  describe('GET /api/rules/monitoring/analytics', () => {
    it('should retrieve rule usage analytics', async () => {
      const { GET } = await import('../monitoring/analytics/route')

      const request = createMockRequest('http://localhost/api/rules/monitoring/analytics?timeRange=7d&includeInsights=true')

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('analytics')
      expect(data.analytics).toHaveProperty('data')
      expect(data.analytics).toHaveProperty('insights')
    })
  })
})

describe('Error Handling', () => {
  it('should handle server errors gracefully', async () => {
    // Mock a service to throw an error
    const mockError = new Error('Test server error')

    const { RuleManagementService } = require('@/lib/services/rule-management')
    const mockService = RuleManagementService.getInstance()
    mockService.createRule.mockRejectedValue(mockError)

    const { POST } = await import('../conditions/route')

    const requestBody = {
      linkId: 'test-link-id',
      type: 'referrer',
      priority: 100,
      isActive: true,
      rules: {},
      action: { type: 'show' }
    }

    const request = createMockRequest('http://localhost/api/rules/conditions', {
      method: 'POST',
      body: requestBody,
      headers: { 'Content-Type': 'application/json' }
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data).toHaveProperty('error')
    expect(data).toHaveProperty('timestamp')
  })
})

describe('Rate Limiting', () => {
  it('should handle rate limiting', async () => {
    // This would test rate limiting if implemented
    // For now, just ensure the endpoint responds
    const { GET } = await import('../conditions/route')

    const request = createMockRequest('http://localhost/api/rules/conditions')
    const response = await GET(request)

    expect(response.status).toBeLessThan(500)
  })
})

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { EnhancedRuleEvaluator } from '@/lib/rule-engine'
import type { ConditionalVisitorContext } from '@/lib/utils/conditional-visitor-context'
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { z } from 'zod'

// Request validation schemas
const performanceTestSchema = z.object({
  linkId: z.string().min(1, 'Link ID is required'),
  testConfiguration: z.object({
    duration: z.number().int().min(1).max(300).default(60), // seconds
    concurrency: z.number().int().min(1).max(100).default(10),
    requestsPerSecond: z.number().int().min(1).max(1000).default(50),
    rampUpTime: z.number().int().min(0).max(60).default(10), // seconds
    testType: z.enum(['load', 'stress', 'spike', 'endurance']).default('load')
  }),
  visitorProfiles: z.array(z.object({
    name: z.string(),
    weight: z.number().min(0).max(1), // probability weight
    context: z.object({
      referrer: z.string().optional(),
      userAgent: z.string().optional(),
      country: z.string().optional(),
      region: z.string().optional(),
      city: z.string().optional(),
      timezone: z.string().optional(),
      deviceType: z.enum(['mobile', 'tablet', 'desktop']).optional(),
      platform: z.string().optional(),
      browser: z.string().optional()
    })
  })).min(1).default([{
    name: 'default',
    weight: 1.0,
    context: {}
  }]),
  options: z.object({
    enableCaching: z.boolean().default(true),
    collectDetailedMetrics: z.boolean().default(true),
    includePercentiles: z.boolean().default(true),
    warmupRequests: z.number().int().min(0).max(100).default(10)
  }).default({})
})

const loadTestResultSchema = z.object({
  testId: z.string(),
  linkId: z.string(),
  configuration: z.object({
    duration: z.number(),
    concurrency: z.number(),
    requestsPerSecond: z.number(),
    testType: z.string()
  }),
  results: z.object({
    totalRequests: z.number(),
    successfulRequests: z.number(),
    failedRequests: z.number(),
    averageResponseTime: z.number(),
    minResponseTime: z.number(),
    maxResponseTime: z.number(),
    percentiles: z.object({
      p50: z.number(),
      p90: z.number(),
      p95: z.number(),
      p99: z.number()
    }),
    throughput: z.number(),
    errorRate: z.number()
  })
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  TEST_FAILED: 'TEST_FAILED',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * POST /api/rules/simulation/performance
 * Run performance tests on rule evaluation
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = performanceTestSchema.parse(body)

    // Initialize rule evaluator
    const evaluator = new EnhancedRuleEvaluator({
      enablePerformanceMonitoring: true,
      enableCaching: validatedData.options.enableCaching
    })

    // Get link with conditions
    const ruleService = RuleManagementService.getInstance()
    const linkWithConditions = await ruleService.getLinkWithConditions(validatedData.linkId)

    if (!linkWithConditions) {
      const error: ApiError = {
        error: 'Link not found',
        code: ERROR_CODES.NOT_FOUND,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 404 })
    }

    // Generate test ID
    const testId = generateTestId()

    // Normalize visitor profile weights
    const totalWeight = validatedData.visitorProfiles.reduce((sum, profile) => sum + profile.weight, 0)
    const normalizedProfiles = validatedData.visitorProfiles.map(profile => ({
      ...profile,
      weight: profile.weight / totalWeight
    }))

    // Run warmup requests if specified
    if (validatedData.options.warmupRequests > 0) {
      await runWarmupRequests(
        linkWithConditions,
        evaluator,
        normalizedProfiles,
        validatedData.options.warmupRequests
      )
    }

    // Run performance test based on type
    let testResults
    switch (validatedData.testConfiguration.testType) {
      case 'load':
        testResults = await runLoadTest(linkWithConditions, evaluator, normalizedProfiles, validatedData)
        break
      case 'stress':
        testResults = await runStressTest(linkWithConditions, evaluator, normalizedProfiles, validatedData)
        break
      case 'spike':
        testResults = await runSpikeTest(linkWithConditions, evaluator, normalizedProfiles, validatedData)
        break
      case 'endurance':
        testResults = await runEnduranceTest(linkWithConditions, evaluator, normalizedProfiles, validatedData)
        break
      default:
        testResults = await runLoadTest(linkWithConditions, evaluator, normalizedProfiles, validatedData)
    }

    // Analyze results
    const analysis = analyzePerformanceResults(testResults)

    return NextResponse.json({
      performanceTest: {
        testId,
        linkId: validatedData.linkId,
        configuration: validatedData.testConfiguration,
        results: testResults,
        analysis,
        recommendations: generatePerformanceRecommendations(analysis),
        metadata: {
          startTime: testResults.startTime,
          endTime: testResults.endTime,
          duration: testResults.duration,
          visitorProfiles: normalizedProfiles.length
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request data',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'performance testing', withErrorContext(request))
  }
}

/**
 * GET /api/rules/simulation/performance
 * Get performance test history and results
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const linkId = searchParams.get('linkId')
    const testId = searchParams.get('testId')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Get performance test history
    const ruleService = RuleManagementService.getInstance()
    
    let testHistory
    if (testId) {
      testHistory = await ruleService.getPerformanceTestResult(testId)
    } else if (linkId) {
      testHistory = await ruleService.getPerformanceTestHistory(linkId, { limit, offset })
    } else {
      testHistory = await ruleService.getUserPerformanceTestHistory(session.user.id, { limit, offset })
    }

    return NextResponse.json({
      testHistory,
      pagination: {
        limit,
        offset,
        total: Array.isArray(testHistory) ? testHistory.length : 1
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return handleApiError(error, 'performance test history retrieval', withErrorContext(request))
  }
}

// Helper functions
async function runWarmupRequests(linkWithConditions: any, evaluator: any, profiles: any[], count: number) {
  const warmupPromises = []
  
  for (let i = 0; i < count; i++) {
    const profile = selectRandomProfile(profiles)
    const context = createVisitorContext(profile.context)
    warmupPromises.push(evaluator.evaluate(linkWithConditions, context))
  }
  
  await Promise.all(warmupPromises)
}

async function runLoadTest(linkWithConditions: any, evaluator: any, profiles: any[], config: any) {
  const startTime = Date.now()
  const results: any[] = []
  const errors: any[] = []
  
  const totalRequests = config.testConfiguration.requestsPerSecond * config.testConfiguration.duration
  const requestInterval = 1000 / config.testConfiguration.requestsPerSecond
  
  // Create request batches
  const batches = []
  for (let i = 0; i < totalRequests; i += config.testConfiguration.concurrency) {
    const batchSize = Math.min(config.testConfiguration.concurrency, totalRequests - i)
    batches.push(batchSize)
  }
  
  // Execute batches
  for (const batchSize of batches) {
    const batchPromises = []
    
    for (let j = 0; j < batchSize; j++) {
      const profile = selectRandomProfile(profiles)
      const context = createVisitorContext(profile.context)
      
      batchPromises.push(
        executeRequest(linkWithConditions, evaluator, context)
          .then(result => results.push(result))
          .catch(error => errors.push(error))
      )
    }
    
    await Promise.all(batchPromises)
    
    // Wait for next batch
    if (batches.indexOf(batchSize) < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, requestInterval * batchSize))
    }
  }
  
  const endTime = Date.now()
  
  return {
    startTime,
    endTime,
    duration: endTime - startTime,
    totalRequests: results.length + errors.length,
    successfulRequests: results.length,
    failedRequests: errors.length,
    results,
    errors
  }
}

async function runStressTest(linkWithConditions: any, evaluator: any, profiles: any[], config: any) {
  // Gradually increase load beyond normal capacity
  const baseRps = config.testConfiguration.requestsPerSecond
  const maxRps = baseRps * 3 // 3x normal load
  const rampUpSteps = 10
  const stepDuration = config.testConfiguration.duration / rampUpSteps
  
  const results: any[] = []
  const errors: any[] = []
  const startTime = Date.now()
  
  for (let step = 0; step < rampUpSteps; step++) {
    const currentRps = baseRps + (maxRps - baseRps) * (step / rampUpSteps)
    const stepResults = await runLoadTestStep(
      linkWithConditions,
      evaluator,
      profiles,
      currentRps,
      stepDuration,
      config.testConfiguration.concurrency
    )
    
    results.push(...stepResults.results)
    errors.push(...stepResults.errors)
  }
  
  const endTime = Date.now()
  
  return {
    startTime,
    endTime,
    duration: endTime - startTime,
    totalRequests: results.length + errors.length,
    successfulRequests: results.length,
    failedRequests: errors.length,
    results,
    errors,
    testType: 'stress'
  }
}

async function runSpikeTest(linkWithConditions: any, evaluator: any, profiles: any[], config: any) {
  // Normal load -> Spike -> Normal load
  const normalRps = config.testConfiguration.requestsPerSecond
  const spikeRps = normalRps * 5 // 5x spike
  const phaseDuration = config.testConfiguration.duration / 3
  
  const results: any[] = []
  const errors: any[] = []
  const startTime = Date.now()
  
  // Phase 1: Normal load
  const phase1 = await runLoadTestStep(linkWithConditions, evaluator, profiles, normalRps, phaseDuration, config.testConfiguration.concurrency)
  results.push(...phase1.results)
  errors.push(...phase1.errors)
  
  // Phase 2: Spike
  const phase2 = await runLoadTestStep(linkWithConditions, evaluator, profiles, spikeRps, phaseDuration, config.testConfiguration.concurrency * 2)
  results.push(...phase2.results)
  errors.push(...phase2.errors)
  
  // Phase 3: Normal load
  const phase3 = await runLoadTestStep(linkWithConditions, evaluator, profiles, normalRps, phaseDuration, config.testConfiguration.concurrency)
  results.push(...phase3.results)
  errors.push(...phase3.errors)
  
  const endTime = Date.now()
  
  return {
    startTime,
    endTime,
    duration: endTime - startTime,
    totalRequests: results.length + errors.length,
    successfulRequests: results.length,
    failedRequests: errors.length,
    results,
    errors,
    testType: 'spike'
  }
}

async function runEnduranceTest(linkWithConditions: any, evaluator: any, profiles: any[], config: any) {
  // Extended duration test with consistent load
  return await runLoadTest(linkWithConditions, evaluator, profiles, {
    ...config,
    testConfiguration: {
      ...config.testConfiguration,
      duration: Math.max(config.testConfiguration.duration, 300) // Minimum 5 minutes
    }
  })
}

async function runLoadTestStep(linkWithConditions: any, evaluator: any, profiles: any[], rps: number, duration: number, concurrency: number) {
  const results: any[] = []
  const errors: any[] = []
  const totalRequests = Math.floor(rps * (duration / 1000))
  const requestInterval = 1000 / rps
  
  const batches = []
  for (let i = 0; i < totalRequests; i += concurrency) {
    const batchSize = Math.min(concurrency, totalRequests - i)
    batches.push(batchSize)
  }
  
  for (const batchSize of batches) {
    const batchPromises = []
    
    for (let j = 0; j < batchSize; j++) {
      const profile = selectRandomProfile(profiles)
      const context = createVisitorContext(profile.context)
      
      batchPromises.push(
        executeRequest(linkWithConditions, evaluator, context)
          .then(result => results.push(result))
          .catch(error => errors.push(error))
      )
    }
    
    await Promise.all(batchPromises)
    
    if (batches.indexOf(batchSize) < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, requestInterval * batchSize))
    }
  }
  
  return { results, errors }
}

async function executeRequest(linkWithConditions: any, evaluator: any, context: any) {
  const startTime = performance.now()
  
  try {
    const result = await evaluator.evaluate(linkWithConditions, context)
    const endTime = performance.now()
    
    return {
      success: true,
      responseTime: endTime - startTime,
      result,
      timestamp: Date.now()
    }
  } catch (error) {
    const endTime = performance.now()
    
    return {
      success: false,
      responseTime: endTime - startTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: Date.now()
    }
  }
}

function selectRandomProfile(profiles: any[]) {
  const random = Math.random()
  let cumulativeWeight = 0
  
  for (const profile of profiles) {
    cumulativeWeight += profile.weight
    if (random <= cumulativeWeight) {
      return profile
    }
  }
  
  return profiles[profiles.length - 1]
}

function createVisitorContext(contextData: any) {
  return new ConditionalVisitorContext({
    ...contextData,
    now: new Date()
  })
}

function analyzePerformanceResults(testResults: any) {
  const successfulResults = testResults.results.filter((r: any) => r.success)
  const responseTimes = successfulResults.map((r: any) => r.responseTime)
  
  if (responseTimes.length === 0) {
    return {
      averageResponseTime: 0,
      minResponseTime: 0,
      maxResponseTime: 0,
      percentiles: { p50: 0, p90: 0, p95: 0, p99: 0 },
      throughput: 0,
      errorRate: 1
    }
  }
  
  responseTimes.sort((a, b) => a - b)
  
  return {
    averageResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
    minResponseTime: responseTimes[0],
    maxResponseTime: responseTimes[responseTimes.length - 1],
    percentiles: {
      p50: responseTimes[Math.floor(responseTimes.length * 0.5)],
      p90: responseTimes[Math.floor(responseTimes.length * 0.9)],
      p95: responseTimes[Math.floor(responseTimes.length * 0.95)],
      p99: responseTimes[Math.floor(responseTimes.length * 0.99)]
    },
    throughput: testResults.successfulRequests / (testResults.duration / 1000),
    errorRate: testResults.failedRequests / testResults.totalRequests
  }
}

function generatePerformanceRecommendations(analysis: any): string[] {
  const recommendations: string[] = []
  
  if (analysis.averageResponseTime > 100) {
    recommendations.push('Consider optimizing rule complexity to reduce evaluation time')
  }
  
  if (analysis.errorRate > 0.01) {
    recommendations.push('High error rate detected - investigate rule evaluation failures')
  }
  
  if (analysis.percentiles.p95 > 200) {
    recommendations.push('High p95 response time - consider adding caching or rule optimization')
  }
  
  if (analysis.throughput < 100) {
    recommendations.push('Low throughput detected - consider scaling or performance optimization')
  }
  
  return recommendations
}

function generateTestId(): string {
  return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { EnhancedRuleEvaluator } from '@/lib/rule-engine'
import type { ConditionalVisitorContext } from '@/lib/utils/conditional-visitor-context'
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { z } from 'zod'

// Request validation schemas
const simulationRequestSchema = z.object({
  linkId: z.string().min(1, 'Link ID is required'),
  visitorContext: z.object({
    referrer: z.string().optional(),
    userAgent: z.string().optional(),
    country: z.string().optional(),
    region: z.string().optional(),
    city: z.string().optional(),
    timezone: z.string().optional(),
    deviceType: z.enum(['mobile', 'tablet', 'desktop']).optional(),
    platform: z.string().optional(),
    browser: z.string().optional(),
    now: z.string().datetime().optional(),
    customHeaders: z.record(z.string()).optional()
  }),
  simulationOptions: z.object({
    includePerformanceMetrics: z.boolean().default(true),
    includeDebugInfo: z.boolean().default(false),
    enableCaching: z.boolean().default(true),
    simulateErrors: z.boolean().default(false),
    errorRate: z.number().min(0).max(1).default(0),
    includeAlternativeOutcomes: z.boolean().default(false)
  }).default({})
})

const batchSimulationSchema = z.object({
  linkId: z.string().min(1, 'Link ID is required'),
  scenarios: z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string().optional(),
    visitorContext: z.object({
      referrer: z.string().optional(),
      userAgent: z.string().optional(),
      country: z.string().optional(),
      region: z.string().optional(),
      city: z.string().optional(),
      timezone: z.string().optional(),
      deviceType: z.enum(['mobile', 'tablet', 'desktop']).optional(),
      platform: z.string().optional(),
      browser: z.string().optional(),
      now: z.string().datetime().optional(),
      customHeaders: z.record(z.string()).optional()
    })
  })).min(1).max(100),
  simulationOptions: z.object({
    includePerformanceMetrics: z.boolean().default(true),
    includeDebugInfo: z.boolean().default(false),
    enableCaching: z.boolean().default(true),
    parallel: z.boolean().default(true),
    maxConcurrency: z.number().int().min(1).max(10).default(5)
  }).default({})
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  SIMULATION_ERROR: 'SIMULATION_ERROR',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * POST /api/rules/simulation/evaluate
 * Simulate rule evaluation for a given visitor context
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = simulationRequestSchema.parse(body)

    // Initialize rule evaluator
    const evaluator = new EnhancedRuleEvaluator({
      enablePerformanceMonitoring: validatedData.simulationOptions.includePerformanceMetrics,
      enableDebugMode: validatedData.simulationOptions.includeDebugInfo,
      enableCaching: validatedData.simulationOptions.enableCaching
    })

    // Create visitor context
    const visitorContext = new ConditionalVisitorContext({
      referrer: validatedData.visitorContext.referrer,
      userAgent: validatedData.visitorContext.userAgent,
      country: validatedData.visitorContext.country,
      region: validatedData.visitorContext.region,
      city: validatedData.visitorContext.city,
      timezone: validatedData.visitorContext.timezone,
      deviceType: validatedData.visitorContext.deviceType,
      platform: validatedData.visitorContext.platform,
      browser: validatedData.visitorContext.browser,
      now: validatedData.visitorContext.now ? new Date(validatedData.visitorContext.now) : new Date(),
      customHeaders: validatedData.visitorContext.customHeaders || {}
    })

    // Get link with conditions
    const ruleService = RuleManagementService.getInstance()
    const linkWithConditions = await ruleService.getLinkWithConditions(validatedData.linkId)

    if (!linkWithConditions) {
      const error: ApiError = {
        error: 'Link not found',
        code: ERROR_CODES.NOT_FOUND,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 404 })
    }

    // Simulate error if requested
    if (validatedData.simulationOptions.simulateErrors && 
        Math.random() < validatedData.simulationOptions.errorRate) {
      throw new Error('Simulated evaluation error')
    }

    // Perform rule evaluation
    const evaluationResult = await evaluator.evaluate(linkWithConditions, visitorContext)

    // Get alternative outcomes if requested
    let alternativeOutcomes = null
    if (validatedData.simulationOptions.includeAlternativeOutcomes) {
      alternativeOutcomes = await getAlternativeOutcomes(linkWithConditions, visitorContext, evaluator)
    }

    return NextResponse.json({
      simulation: {
        linkId: validatedData.linkId,
        result: evaluationResult,
        visitorContext: {
          processed: visitorContext.toJSON(),
          original: validatedData.visitorContext
        },
        alternativeOutcomes,
        metadata: {
          simulationId: generateSimulationId(),
          timestamp: new Date().toISOString(),
          options: validatedData.simulationOptions
        }
      },
      analysis: {
        conditionsEvaluated: evaluationResult.matchedConditions.length,
        evaluationPath: evaluationResult.metadata.debugInfo?.evaluationPath,
        performanceImpact: evaluationResult.performanceMetrics,
        cacheUtilization: evaluationResult.metadata.debugInfo?.cacheUtilization
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request data',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'rule evaluation simulation', withErrorContext(request))
  }
}

/**
 * POST /api/rules/simulation/evaluate/batch
 * Simulate rule evaluation for multiple scenarios
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = batchSimulationSchema.parse(body)

    // Initialize rule evaluator
    const evaluator = new EnhancedRuleEvaluator({
      enablePerformanceMonitoring: validatedData.simulationOptions.includePerformanceMetrics,
      enableDebugMode: validatedData.simulationOptions.includeDebugInfo,
      enableCaching: validatedData.simulationOptions.enableCaching
    })

    // Get link with conditions
    const ruleService = RuleManagementService.getInstance()
    const linkWithConditions = await ruleService.getLinkWithConditions(validatedData.linkId)

    if (!linkWithConditions) {
      const error: ApiError = {
        error: 'Link not found',
        code: ERROR_CODES.NOT_FOUND,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 404 })
    }

    // Process scenarios
    const results = []
    const startTime = Date.now()

    if (validatedData.simulationOptions.parallel) {
      // Process scenarios in parallel with concurrency limit
      const chunks = chunkArray(validatedData.scenarios, validatedData.simulationOptions.maxConcurrency)
      
      for (const chunk of chunks) {
        const chunkResults = await Promise.all(
          chunk.map(scenario => processScenario(scenario, linkWithConditions, evaluator))
        )
        results.push(...chunkResults)
      }
    } else {
      // Process scenarios sequentially
      for (const scenario of validatedData.scenarios) {
        const result = await processScenario(scenario, linkWithConditions, evaluator)
        results.push(result)
      }
    }

    const totalTime = Date.now() - startTime

    // Analyze batch results
    const analysis = analyzeBatchResults(results)

    return NextResponse.json({
      batchSimulation: {
        linkId: validatedData.linkId,
        totalScenarios: validatedData.scenarios.length,
        results,
        analysis,
        performance: {
          totalExecutionTime: totalTime,
          averageExecutionTime: totalTime / validatedData.scenarios.length,
          parallel: validatedData.simulationOptions.parallel,
          maxConcurrency: validatedData.simulationOptions.maxConcurrency
        },
        metadata: {
          batchId: generateSimulationId(),
          timestamp: new Date().toISOString(),
          options: validatedData.simulationOptions
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request data',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'batch rule evaluation simulation', withErrorContext(request))
  }
}

// Helper functions
async function getAlternativeOutcomes(linkWithConditions: any, visitorContext: any, evaluator: any) {
  // Simulate different priority orders and rule modifications
  const alternatives = []
  
  // Try with different priority orders
  const shuffledConditions = [...linkWithConditions.conditions].sort(() => Math.random() - 0.5)
  const altLink = { ...linkWithConditions, conditions: shuffledConditions }
  const altResult = await evaluator.evaluate(altLink, visitorContext)
  
  alternatives.push({
    type: 'priority_shuffle',
    description: 'Result with randomized priority order',
    result: altResult
  })
  
  return alternatives
}

async function processScenario(scenario: any, linkWithConditions: any, evaluator: any) {
  try {
    const visitorContext = new ConditionalVisitorContext({
      ...scenario.visitorContext,
      now: scenario.visitorContext.now ? new Date(scenario.visitorContext.now) : new Date()
    })
    
    const result = await evaluator.evaluate(linkWithConditions, visitorContext)
    
    return {
      scenarioId: scenario.id,
      scenarioName: scenario.name,
      success: true,
      result,
      visitorContext: visitorContext.toJSON()
    }
  } catch (error) {
    return {
      scenarioId: scenario.id,
      scenarioName: scenario.name,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      visitorContext: scenario.visitorContext
    }
  }
}

function analyzeBatchResults(results: any[]) {
  const successful = results.filter(r => r.success)
  const failed = results.filter(r => !r.success)
  
  const outcomes = successful.reduce((acc, r) => {
    const outcome = r.result.shouldShow ? 'show' : 'hide'
    acc[outcome] = (acc[outcome] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  return {
    totalScenarios: results.length,
    successful: successful.length,
    failed: failed.length,
    successRate: successful.length / results.length,
    outcomes,
    averageEvaluationTime: successful.reduce((acc, r) => acc + r.result.evaluationTime, 0) / successful.length,
    errors: failed.map(f => ({ scenarioId: f.scenarioId, error: f.error }))
  }
}

function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }
  return chunks
}

function generateSimulationId(): string {
  return `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { LinkConditionRepository } from '@/lib/repositories/link-condition'
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { ErrorFactory } from '@/lib/errors/app-errors'
import { z } from 'zod'

// Request validation schemas
const createConditionSchema = z.object({
  linkId: z.string().min(1, 'Link ID is required'),
  type: z.enum(['referrer', 'location', 'device', 'time', 'schedule']),
  priority: z.number().int().min(0).max(1000),
  isActive: z.boolean().default(true),
  rules: z.record(z.any()),
  action: z.object({
    type: z.enum(['show', 'hide', 'redirect']),
    value: z.string().optional(),
    alternateTitle: z.string().optional(),
    alternateIcon: z.string().optional(),
    metadata: z.record(z.any()).optional()
  })
})

const updateConditionSchema = createConditionSchema.partial().omit({ linkId: true })

const querySchema = z.object({
  linkId: z.string().optional(),
  type: z.enum(['referrer', 'location', 'device', 'time', 'schedule']).optional(),
  isActive: z.boolean().optional(),
  priority: z.number().int().optional(),
  limit: z.number().int().min(1).max(100).default(50),
  offset: z.number().int().min(0).default(0),
  sortBy: z.enum(['priority', 'createdAt', 'updatedAt']).default('priority'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  RATE_LIMITED: 'RATE_LIMITED',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * GET /api/rules/conditions
 * Retrieve conditions with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const rawParams = Object.fromEntries(searchParams.entries())
    
    // Convert string parameters to appropriate types
    const processedParams = {
      ...rawParams,
      isActive: rawParams.isActive ? rawParams.isActive === 'true' : undefined,
      priority: rawParams.priority ? parseInt(rawParams.priority) : undefined,
      limit: rawParams.limit ? parseInt(rawParams.limit) : undefined,
      offset: rawParams.offset ? parseInt(rawParams.offset) : undefined
    }

    const validatedParams = querySchema.parse(processedParams)

    // Get conditions based on query parameters
    let conditions
    if (validatedParams.linkId) {
      conditions = await LinkConditionRepository.findByLinkId(validatedParams.linkId)
    } else {
      conditions = await LinkConditionRepository.findMany({
        where: {
          ...(validatedParams.type && { type: validatedParams.type }),
          ...(validatedParams.isActive !== undefined && { isActive: validatedParams.isActive }),
          ...(validatedParams.priority !== undefined && { priority: validatedParams.priority })
        },
        orderBy: {
          [validatedParams.sortBy]: validatedParams.sortOrder
        },
        take: validatedParams.limit,
        skip: validatedParams.offset
      })
    }

    // Filter conditions based on user permissions (only their own links)
    // This would require checking link ownership - simplified for now
    
    return NextResponse.json({
      conditions,
      pagination: {
        limit: validatedParams.limit,
        offset: validatedParams.offset,
        total: conditions.length
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request parameters',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'condition retrieval', withErrorContext(request))
  }
}

/**
 * POST /api/rules/conditions
 * Create a new condition with validation and conflict checking
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createConditionSchema.parse(body)

    // Initialize rule management service
    const ruleService = RuleManagementService.getInstance({
      autoResolveConflicts: true,
      enablePerformanceMonitoring: true,
      validationLevel: 'moderate'
    })

    // Create the condition with comprehensive validation
    const result = await ruleService.createRule(
      validatedData.linkId,
      validatedData,
      {
        autoResolveConflicts: true,
        enableOptimization: true,
        validationLevel: 'moderate'
      }
    )

    if (!result.success) {
      const error: ApiError = {
        error: result.error || 'Failed to create condition',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: result.validationErrors,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 400 })
    }

    return NextResponse.json({
      condition: result.data,
      conflicts: result.conflicts,
      warnings: result.warnings,
      performanceMetrics: result.performanceMetrics,
      timestamp: new Date().toISOString()
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request data',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'condition creation', withErrorContext(request))
  }
}

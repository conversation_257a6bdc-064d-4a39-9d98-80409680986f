import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { LinkConditionRepository } from '@/lib/repositories/link-condition'
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { ErrorFactory } from '@/lib/errors/app-errors'
import { z } from 'zod'

// Request validation schemas
const updateConditionSchema = z.object({
  type: z.enum(['referrer', 'location', 'device', 'time', 'schedule']).optional(),
  priority: z.number().int().min(0).max(1000).optional(),
  isActive: z.boolean().optional(),
  rules: z.record(z.any()).optional(),
  action: z.object({
    type: z.enum(['show', 'hide', 'redirect']),
    value: z.string().optional(),
    alternateTitle: z.string().optional(),
    alternateIcon: z.string().optional(),
    metadata: z.record(z.any()).optional()
  }).optional()
}).refine(data => Object.keys(data).length > 0, {
  message: "At least one field must be provided for update"
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  FORBIDDEN: 'FORBIDDEN',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * GET /api/rules/conditions/[conditionId]
 * Retrieve a specific condition by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { conditionId: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { conditionId } = params

    if (!conditionId) {
      const error: ApiError = {
        error: 'Condition ID is required',
        code: ERROR_CODES.VALIDATION_ERROR,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 400 })
    }

    const condition = await LinkConditionRepository.findById(conditionId)

    if (!condition) {
      const error: ApiError = {
        error: 'Condition not found',
        code: ERROR_CODES.NOT_FOUND,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 404 })
    }

    // TODO: Add ownership verification - check if the condition belongs to user's link

    return NextResponse.json({
      condition,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return handleApiError(error, 'condition retrieval', withErrorContext(request))
  }
}

/**
 * PUT /api/rules/conditions/[conditionId]
 * Update a specific condition
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { conditionId: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { conditionId } = params

    if (!conditionId) {
      const error: ApiError = {
        error: 'Condition ID is required',
        code: ERROR_CODES.VALIDATION_ERROR,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 400 })
    }

    const body = await request.json()
    const validatedData = updateConditionSchema.parse(body)

    // Check if condition exists
    const existingCondition = await LinkConditionRepository.findById(conditionId)
    if (!existingCondition) {
      const error: ApiError = {
        error: 'Condition not found',
        code: ERROR_CODES.NOT_FOUND,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 404 })
    }

    // TODO: Add ownership verification

    // Initialize rule management service
    const ruleService = RuleManagementService.getInstance({
      autoResolveConflicts: true,
      enablePerformanceMonitoring: true,
      validationLevel: 'moderate'
    })

    // Update the condition with validation
    const result = await ruleService.updateRule(
      conditionId,
      validatedData,
      {
        autoResolveConflicts: true,
        enableOptimization: true,
        validationLevel: 'moderate'
      }
    )

    if (!result.success) {
      const error: ApiError = {
        error: result.error || 'Failed to update condition',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: result.validationErrors,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 400 })
    }

    return NextResponse.json({
      condition: result.data,
      conflicts: result.conflicts,
      warnings: result.warnings,
      performanceMetrics: result.performanceMetrics,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request data',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'condition update', withErrorContext(request))
  }
}

/**
 * DELETE /api/rules/conditions/[conditionId]
 * Delete a specific condition
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { conditionId: string } }
) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { conditionId } = params

    if (!conditionId) {
      const error: ApiError = {
        error: 'Condition ID is required',
        code: ERROR_CODES.VALIDATION_ERROR,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 400 })
    }

    // Check if condition exists
    const existingCondition = await LinkConditionRepository.findById(conditionId)
    if (!existingCondition) {
      const error: ApiError = {
        error: 'Condition not found',
        code: ERROR_CODES.NOT_FOUND,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 404 })
    }

    // TODO: Add ownership verification

    // Initialize rule management service
    const ruleService = RuleManagementService.getInstance()

    // Delete the condition
    const result = await ruleService.deleteRule(conditionId)

    if (!result.success) {
      const error: ApiError = {
        error: result.error || 'Failed to delete condition',
        code: ERROR_CODES.SERVER_ERROR,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Condition deleted successfully',
      conditionId,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return handleApiError(error, 'condition deletion', withErrorContext(request))
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { z } from 'zod'

// Request validation schemas
const conflictDetectionSchema = z.object({
  linkId: z.string().min(1, 'Link ID is required'),
  includeWarnings: z.boolean().default(true),
  includeSuggestions: z.boolean().default(true),
  analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed')
})

const conflictResolutionSchema = z.object({
  linkId: z.string().min(1, 'Link ID is required'),
  resolutionStrategy: z.enum(['auto', 'priority_adjustment', 'rule_modification', 'manual']).default('auto'),
  conflictIds: z.array(z.string()).optional(),
  dryRun: z.boolean().default(false),
  preserveUserPreferences: z.boolean().default(true)
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT_RESOLUTION_FAILED: 'CONFLICT_RESOLUTION_FAILED',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * GET /api/rules/conditions/conflicts
 * Detect conflicts in rule configurations
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const rawParams = Object.fromEntries(searchParams.entries())
    
    // Convert string parameters to appropriate types
    const processedParams = {
      ...rawParams,
      includeWarnings: rawParams.includeWarnings !== 'false',
      includeSuggestions: rawParams.includeSuggestions !== 'false'
    }

    const validatedParams = conflictDetectionSchema.parse(processedParams)

    // Initialize rule management service
    const ruleService = RuleManagementService.getInstance()

    // Detect conflicts
    const conflictResult = await ruleService.detectConflicts(validatedParams.linkId)

    // Get detailed analysis if requested
    let detailedAnalysis = null
    if (validatedParams.analysisDepth === 'comprehensive') {
      detailedAnalysis = await ruleService.analyzeRuleComplexity(validatedParams.linkId)
    }

    // Get performance impact analysis
    const performanceAnalysis = await ruleService.analyzePerformanceImpact(validatedParams.linkId)

    return NextResponse.json({
      linkId: validatedParams.linkId,
      hasConflicts: conflictResult.hasConflicts,
      conflicts: conflictResult.conflicts,
      warnings: validatedParams.includeWarnings ? conflictResult.warnings : [],
      suggestions: validatedParams.includeSuggestions ? conflictResult.suggestions : [],
      analysis: {
        totalRules: conflictResult.conflicts.reduce((acc, c) => acc + c.affectedRules.length, 0),
        conflictTypes: conflictResult.conflicts.reduce((acc, c) => {
          acc[c.type] = (acc[c.type] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        severityBreakdown: conflictResult.conflicts.reduce((acc, c) => {
          acc[c.severity] = (acc[c.severity] || 0) + 1
          return acc
        }, {} as Record<string, number>)
      },
      performance: {
        estimatedEvaluationTime: performanceAnalysis.estimatedEvaluationTime,
        complexityScore: performanceAnalysis.complexityScore,
        optimizationOpportunities: performanceAnalysis.optimizationOpportunities
      },
      detailedAnalysis: detailedAnalysis,
      metadata: {
        analysisDepth: validatedParams.analysisDepth,
        includeWarnings: validatedParams.includeWarnings,
        includeSuggestions: validatedParams.includeSuggestions
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request parameters',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'conflict detection', withErrorContext(request))
  }
}

/**
 * POST /api/rules/conditions/conflicts
 * Resolve conflicts in rule configurations
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = conflictResolutionSchema.parse(body)

    // Initialize rule management service
    const ruleService = RuleManagementService.getInstance({
      autoResolveConflicts: true,
      enableOptimization: true
    })

    // Resolve conflicts based on strategy
    let resolutionResult
    switch (validatedData.resolutionStrategy) {
      case 'auto':
        resolutionResult = await ruleService.resolveConflicts(
          validatedData.linkId,
          {
            strategy: 'auto',
            dryRun: validatedData.dryRun,
            preserveUserPreferences: validatedData.preserveUserPreferences
          }
        )
        break
      
      case 'priority_adjustment':
        resolutionResult = await ruleService.resolveConflicts(
          validatedData.linkId,
          {
            strategy: 'priority_adjustment',
            dryRun: validatedData.dryRun,
            conflictIds: validatedData.conflictIds
          }
        )
        break
      
      case 'rule_modification':
        resolutionResult = await ruleService.resolveConflicts(
          validatedData.linkId,
          {
            strategy: 'rule_modification',
            dryRun: validatedData.dryRun,
            conflictIds: validatedData.conflictIds
          }
        )
        break
      
      default:
        // Manual resolution - just return analysis
        const conflictAnalysis = await ruleService.detectConflicts(validatedData.linkId)
        resolutionResult = {
          success: true,
          strategy: 'manual',
          conflicts: conflictAnalysis.conflicts,
          suggestions: conflictAnalysis.suggestions,
          requiresManualIntervention: true
        }
    }

    if (!resolutionResult.success && !validatedData.dryRun) {
      const error: ApiError = {
        error: resolutionResult.error || 'Failed to resolve conflicts',
        code: ERROR_CODES.CONFLICT_RESOLUTION_FAILED,
        details: resolutionResult.details,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 400 })
    }

    return NextResponse.json({
      success: resolutionResult.success,
      strategy: validatedData.resolutionStrategy,
      dryRun: validatedData.dryRun,
      resolution: {
        conflictsResolved: resolutionResult.conflictsResolved || 0,
        rulesModified: resolutionResult.rulesModified || 0,
        prioritiesAdjusted: resolutionResult.prioritiesAdjusted || 0,
        rulesDeactivated: resolutionResult.rulesDeactivated || 0
      },
      changes: resolutionResult.changes || [],
      remainingConflicts: resolutionResult.remainingConflicts || [],
      suggestions: resolutionResult.suggestions || [],
      requiresManualIntervention: resolutionResult.requiresManualIntervention || false,
      performanceImpact: resolutionResult.performanceImpact,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request data',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'conflict resolution', withErrorContext(request))
  }
}

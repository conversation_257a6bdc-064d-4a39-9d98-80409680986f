import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { z } from 'zod'

// Request validation schemas
const performanceQuerySchema = z.object({
  linkId: z.string().optional(),
  conditionId: z.string().optional(),
  timeRange: z.enum(['1h', '24h', '7d', '30d', '90d']).default('24h'),
  includeHistorical: z.boolean().default(true),
  includeBreakdown: z.boolean().default(true),
  includeOptimizationSuggestions: z.boolean().default(true),
  aggregation: z.enum(['avg', 'p50', 'p95', 'p99', 'max']).default('p95')
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * GET /api/rules/conditions/performance
 * Get performance metrics for rule evaluation
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const rawParams = Object.fromEntries(searchParams.entries())
    
    // Convert string parameters to appropriate types
    const processedParams = {
      ...rawParams,
      includeHistorical: rawParams.includeHistorical !== 'false',
      includeBreakdown: rawParams.includeBreakdown !== 'false',
      includeOptimizationSuggestions: rawParams.includeOptimizationSuggestions !== 'false'
    }

    const validatedParams = performanceQuerySchema.parse(processedParams)

    // Initialize rule management service
    const ruleService = RuleManagementService.getInstance({
      enablePerformanceMonitoring: true
    })

    // Get performance metrics
    let performanceMetrics
    if (validatedParams.linkId) {
      performanceMetrics = await ruleService.getPerformanceMetrics(validatedParams.linkId, {
        timeRange: validatedParams.timeRange,
        includeHistorical: validatedParams.includeHistorical,
        aggregation: validatedParams.aggregation
      })
    } else if (validatedParams.conditionId) {
      performanceMetrics = await ruleService.getConditionPerformanceMetrics(validatedParams.conditionId, {
        timeRange: validatedParams.timeRange,
        includeHistorical: validatedParams.includeHistorical,
        aggregation: validatedParams.aggregation
      })
    } else {
      // Get overall performance metrics for user's rules
      performanceMetrics = await ruleService.getOverallPerformanceMetrics(session.user.id, {
        timeRange: validatedParams.timeRange,
        includeHistorical: validatedParams.includeHistorical,
        aggregation: validatedParams.aggregation
      })
    }

    // Get performance breakdown if requested
    let performanceBreakdown = null
    if (validatedParams.includeBreakdown && validatedParams.linkId) {
      performanceBreakdown = await ruleService.getPerformanceBreakdown(validatedParams.linkId)
    }

    // Get optimization suggestions if requested
    let optimizationSuggestions = null
    if (validatedParams.includeOptimizationSuggestions && validatedParams.linkId) {
      const optimizationResult = await ruleService.analyzePerformanceOptimization(validatedParams.linkId)
      optimizationSuggestions = optimizationResult.suggestions
    }

    // Calculate performance health score
    const healthScore = calculatePerformanceHealthScore(performanceMetrics)

    return NextResponse.json({
      metrics: {
        evaluationTime: {
          current: performanceMetrics.currentEvaluationTime,
          average: performanceMetrics.averageEvaluationTime,
          percentile: {
            p50: performanceMetrics.p50EvaluationTime,
            p95: performanceMetrics.p95EvaluationTime,
            p99: performanceMetrics.p99EvaluationTime
          },
          trend: performanceMetrics.evaluationTimeTrend
        },
        throughput: {
          evaluationsPerSecond: performanceMetrics.evaluationsPerSecond,
          peakThroughput: performanceMetrics.peakThroughput,
          averageThroughput: performanceMetrics.averageThroughput
        },
        errorRate: {
          current: performanceMetrics.currentErrorRate,
          average: performanceMetrics.averageErrorRate,
          trend: performanceMetrics.errorRateTrend
        },
        cachePerformance: {
          hitRate: performanceMetrics.cacheHitRate,
          missRate: performanceMetrics.cacheMissRate,
          averageHitTime: performanceMetrics.averageCacheHitTime,
          averageMissTime: performanceMetrics.averageCacheMissTime
        }
      },
      breakdown: performanceBreakdown ? {
        conditionEvaluationTimes: performanceBreakdown.conditionEvaluationTimes,
        visitorContextParsingTime: performanceBreakdown.visitorContextParsingTime,
        databaseQueryTime: performanceBreakdown.databaseQueryTime,
        validationTime: performanceBreakdown.validationTime,
        conflictResolutionTime: performanceBreakdown.conflictResolutionTime
      } : null,
      optimization: optimizationSuggestions ? {
        suggestions: optimizationSuggestions,
        potentialImprovements: optimizationSuggestions.map(s => ({
          type: s.type,
          impact: s.estimatedImpact,
          effort: s.implementationEffort
        }))
      } : null,
      health: {
        score: healthScore,
        status: getHealthStatus(healthScore),
        alerts: getPerformanceAlerts(performanceMetrics),
        recommendations: getPerformanceRecommendations(performanceMetrics)
      },
      metadata: {
        timeRange: validatedParams.timeRange,
        aggregation: validatedParams.aggregation,
        linkId: validatedParams.linkId,
        conditionId: validatedParams.conditionId,
        dataPoints: performanceMetrics.dataPoints || 0
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid request parameters',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'performance metrics retrieval', withErrorContext(request))
  }
}

// Helper functions
function calculatePerformanceHealthScore(metrics: any): number {
  // Simple health score calculation based on key metrics
  let score = 100
  
  // Penalize high evaluation times
  if (metrics.p95EvaluationTime > 50) score -= 20
  else if (metrics.p95EvaluationTime > 20) score -= 10
  
  // Penalize high error rates
  if (metrics.currentErrorRate > 0.05) score -= 30
  else if (metrics.currentErrorRate > 0.01) score -= 15
  
  // Penalize low cache hit rates
  if (metrics.cacheHitRate < 0.5) score -= 20
  else if (metrics.cacheHitRate < 0.8) score -= 10
  
  return Math.max(0, score)
}

function getHealthStatus(score: number): string {
  if (score >= 90) return 'excellent'
  if (score >= 75) return 'good'
  if (score >= 60) return 'fair'
  if (score >= 40) return 'poor'
  return 'critical'
}

function getPerformanceAlerts(metrics: any): string[] {
  const alerts: string[] = []
  
  if (metrics.p95EvaluationTime > 100) {
    alerts.push('High evaluation latency detected')
  }
  
  if (metrics.currentErrorRate > 0.05) {
    alerts.push('High error rate detected')
  }
  
  if (metrics.cacheHitRate < 0.5) {
    alerts.push('Low cache hit rate detected')
  }
  
  return alerts
}

function getPerformanceRecommendations(metrics: any): string[] {
  const recommendations: string[] = []
  
  if (metrics.p95EvaluationTime > 50) {
    recommendations.push('Consider optimizing rule complexity or adding caching')
  }
  
  if (metrics.cacheHitRate < 0.8) {
    recommendations.push('Review cache configuration and TTL settings')
  }
  
  if (metrics.currentErrorRate > 0.01) {
    recommendations.push('Investigate and fix rule evaluation errors')
  }
  
  return recommendations
}

'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Activity, Clock, AlertTriangle, TrendingUp, TrendingDown, Zap } from 'lucide-react'
import { toast } from 'sonner'

interface PerformanceMonitoringDashboardProps {
  userId: string
}

interface PerformanceMetrics {
  averageEvaluationTime: number
  p95EvaluationTime: number
  p99EvaluationTime: number
  totalEvaluations: number
  errorRate: number
  cacheHitRate: number
  slowestRules: Array<{
    id: string
    linkTitle: string
    type: string
    averageTime: number
    evaluationCount: number
  }>
  performanceAlerts: Array<{
    id: string
    type: 'slow_evaluation' | 'high_error_rate' | 'cache_miss_spike'
    message: string
    severity: 'warning' | 'error'
    timestamp: string
  }>
  timeSeriesData: Array<{
    timestamp: string
    averageTime: number
    errorRate: number
    evaluationCount: number
  }>
}

export function PerformanceMonitoringDashboard({ userId }: PerformanceMonitoringDashboardProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [timeRange, setTimeRange] = useState('24h')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPerformanceMetrics = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/rules/monitoring/metrics?timeRange=${timeRange}`)
        if (!response.ok) {
          throw new Error('Failed to fetch performance metrics')
        }

        const apiResponse = await response.json()

        // Transform the API response to match the expected PerformanceMetrics interface
        const transformedMetrics: PerformanceMetrics = {
          averageEvaluationTime: apiResponse.metrics?.data?.metrics?.evaluation_time?.avg || 0,
          p95EvaluationTime: apiResponse.metrics?.data?.metrics?.evaluation_time?.p95 || 0,
          p99EvaluationTime: apiResponse.metrics?.data?.metrics?.evaluation_time?.p99 || 0,
          totalEvaluations: apiResponse.metrics?.data?.metrics?.throughput?.total || 0,
          errorRate: apiResponse.metrics?.data?.metrics?.error_rate?.percentage || 0,
          cacheHitRate: apiResponse.metrics?.data?.metrics?.cache_hit_rate?.percentage || 0,
          slowestRules: [], // Mock data for now
          performanceAlerts: (apiResponse.metrics?.health?.alerts || []).map((alert: any) => ({
            id: alert.id || `alert-${Date.now()}-${Math.random()}`,
            type: alert.metricType === 'evaluation_time' ? 'slow_evaluation' :
                  alert.metricType === 'error_rate' ? 'high_error_rate' : 'cache_miss_spike',
            message: alert.message || 'Performance alert detected',
            severity: alert.severity === 'critical' ? 'error' : 'warning',
            timestamp: alert.timestamp || alert.triggeredAt || new Date().toISOString()
          })),
          timeSeriesData: (apiResponse.metrics?.data?.dataPoints || []).map((point: any) => ({
            timestamp: point.timestamp,
            averageTime: point.value || 0,
            errorRate: 0, // Mock for now
            evaluationCount: point.count || 0
          }))
        }

        setMetrics(transformedMetrics)
      } catch (error) {
        console.error('Performance metrics fetch error:', error)
        setError(error instanceof Error ? error.message : 'Failed to load performance metrics')
        toast.error('Failed to load performance metrics')
      } finally {
        setIsLoading(false)
      }
    }

    fetchPerformanceMetrics()
  }, [userId, timeRange])

  const getPerformanceStatus = (averageTime: number) => {
    if (averageTime < 10) return { status: 'excellent', color: 'text-green-600', icon: TrendingUp }
    if (averageTime < 50) return { status: 'good', color: 'text-blue-600', icon: Activity }
    if (averageTime < 100) return { status: 'fair', color: 'text-yellow-600', icon: Clock }
    return { status: 'poor', color: 'text-red-600', icon: TrendingDown }
  }

  const getSeverityBadge = (severity: string) => {
    return severity === 'error' ? 'destructive' : 'secondary'
  }

  const formatAlertTimestamp = (timestamp: string | Date | null | undefined): string => {
    if (!timestamp) {
      return 'Unknown time'
    }

    try {
      const date = new Date(timestamp)

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid date'
      }

      const now = new Date()
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

      // Format relative time for recent alerts
      if (diffInMinutes < 1) {
        return 'Just now'
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes} min ago`
      } else if (diffInMinutes < 1440) { // Less than 24 hours
        const hours = Math.floor(diffInMinutes / 60)
        return `${hours} hour${hours > 1 ? 's' : ''} ago`
      } else {
        // For older alerts, show the actual date and time
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit'
        })
      }
    } catch (error) {
      console.error('Error formatting alert timestamp:', error)
      return 'Invalid date'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button 
            variant="outline" 
            size="sm" 
            className="ml-2"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  if (!metrics) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center text-muted-foreground">
            No performance data available
          </div>
        </CardContent>
      </Card>
    )
  }

  const performanceStatus = getPerformanceStatus(metrics.averageEvaluationTime)
  const StatusIcon = performanceStatus.icon

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Performance Metrics</h3>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1h">Last Hour</SelectItem>
            <SelectItem value="24h">Last 24h</SelectItem>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Time</CardTitle>
            <StatusIcon className={`h-4 w-4 ${performanceStatus.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.averageEvaluationTime}ms</div>
            <p className="text-xs text-muted-foreground">
              {performanceStatus.status} performance
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">P95 Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.p95EvaluationTime}ms</div>
            <p className="text-xs text-muted-foreground">
              95th percentile
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(metrics.errorRate * 100).toFixed(2)}%</div>
            <p className="text-xs text-muted-foreground">
              {metrics.totalEvaluations} evaluations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(metrics.cacheHitRate * 100).toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Cache efficiency
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Alerts */}
      {metrics.performanceAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Performance Alerts
            </CardTitle>
            <CardDescription>
              Recent performance issues that require attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.performanceAlerts.map((alert) => (
                <div key={alert.id} className="flex items-start justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant={getSeverityBadge(alert.severity)}>
                        {alert.severity}
                      </Badge>
                      <span className="text-sm font-medium">{alert.type.replace('_', ' ')}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{alert.message}</p>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {formatAlertTimestamp(alert.timestamp)}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Slowest Rules */}
      <Card>
        <CardHeader>
          <CardTitle>Slowest Rules</CardTitle>
          <CardDescription>
            Rules with the highest average evaluation times
          </CardDescription>
        </CardHeader>
        <CardContent>
          {metrics.slowestRules.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              No performance data available for individual rules
            </div>
          ) : (
            <div className="space-y-3">
              {metrics.slowestRules.map((rule, index) => (
                <div key={rule.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline">#{index + 1}</Badge>
                    <div>
                      <p className="font-medium">{rule.linkTitle}</p>
                      <p className="text-sm text-muted-foreground">
                        {rule.type} rule • {rule.evaluationCount} evaluations
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold">{rule.averageTime}ms</div>
                    <div className="text-xs text-muted-foreground">average</div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Recommendations */}
      <Alert>
        <Activity className="h-4 w-4" />
        <AlertDescription>
          <strong>Performance Tips:</strong> Keep rule evaluation under 50ms for optimal user experience. 
          Consider simplifying complex rules, using caching for frequently evaluated conditions, 
          and reviewing rules with high error rates.
        </AlertDescription>
      </Alert>
    </div>
  )
}
